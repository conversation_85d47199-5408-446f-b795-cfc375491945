# 🔐 Система управления ролями

## Обзор

Реализована полная система управления ролями и разрешениями с возможностью создания, редактирования и просмотра ролей пользователей.

## ✨ Новые возможности

### 1. Страница управления ролями
- **Список всех ролей** с детальной информацией
- **Поиск и фильтрация** ролей
- **Статистика** по ролям и пользователям
- **Создание новых ролей** с настройкой разрешений

### 2. Система разрешений
- **Гранулярные разрешения** по категориям
- **Категории разрешений**: Клиенты, SIP, DID, Пользователи, Поддержка, Биллинг, Отчеты, Настройки, Логи
- **Детальное описание** каждого разрешения
- **Групповое управление** разрешениями по категориям

### 3. Типы ролей
- **Системные роли**: Провайдер, Реселлер, Администратор, Поддержка
- **Пользовательские роли**: Создаваемые администраторами
- **Защита системных ролей** от изменения и удаления

## 🎯 Интерфейс

### Главная страница ролей
```
┌─────────────────────────────────────────┐
│ 🔐 Роли и разрешения                    │
├─────────────────────────────────────────┤
│ 📊 5 ролей | 4 системные | 12 польз.   │
│                                         │
│ [Поиск ролей...]        [➕ Создать]   │
│                                         │
│ 🛡️ Провайдер    | Все разрешения       │
│ 👥 Реселлер     | 8 разрешений         │
│ ⚙️ Администратор | 15 разрешений        │
│ 👁️ Поддержка     | 6 разрешений         │
│ 🔒 Менеджер     | 7 разрешений         │
└─────────────────────────────────────────┘
```

### Создание/редактирование роли
```
┌─────────────────────────────────────────┐
│ ➕ Создание роли                        │
├─────────────────────────────────────────┤
│ Системное имя: [manager_____________]   │
│ Отображаемое:  [Менеджер____________]   │
│ Описание:      [Управление продажами]   │
│                                         │
│ 🔐 Разрешения (7)                       │
│ [Поиск] [Категория▼]                   │
│                                         │
│ ✅ Клиенты                              │
│ ☑️ Просмотр клиентов                    │
│ ☑️ Создание клиентов                    │
│ ☑️ Редактирование клиентов              │
│ ☐ Удаление клиентов                     │
│                                         │
│ [Отмена] [🛡️ Создать роль]             │
└─────────────────────────────────────────┘
```

### Просмотр деталей роли
```
┌─────────────────────────────────────────┐
│ 👁️ Детали роли                          │
├─────────────────────────────────────────┤
│ 🛡️ Провайдер [Системная]               │
│ provider                                │
│ Полный доступ ко всем функциям          │
│                                         │
│ 👥 1 польз. | 🔐 Все | 📅 01.01.24     │
│                                         │
│ 🔐 Разрешения                           │
│ 🛡️ Полный доступ                        │
│ Эта роль имеет доступ ко всем функциям  │
│                                         │
│ ⚠️ Системная роль                       │
│ Не может быть изменена или удалена      │
└─────────────────────────────────────────┘
```

## 🔧 Технические детали

### Новые компоненты
- `Roles.tsx` - основная страница управления ролями
- `RoleModal.tsx` - модальное окно создания/редактирования
- `RoleDetailsModal.tsx` - просмотр деталей роли

### Структура данных

#### Роль
```typescript
interface Role {
  role_id: string
  name: string              // Системное имя (provider, admin, etc.)
  display_name: string      // Отображаемое имя
  description: string       // Описание роли
  permissions: string[]     // Массив разрешений
  is_system: boolean        // Системная роль?
  created_at: string
  updated_at: string
  users_count: number       // Количество пользователей
}
```

#### Разрешение
```typescript
interface Permission {
  id: string                // clients.read, users.create, etc.
  name: string              // Системное имя
  display_name: string      // Отображаемое имя
  category: string          // Категория (Клиенты, SIP, etc.)
  description: string       // Описание разрешения
}
```

### Системные роли

#### 🛡️ Провайдер (provider)
- **Разрешения**: Все (`*`)
- **Описание**: Полный доступ ко всем функциям системы
- **Пользователи**: Владельцы платформы

#### 👥 Реселлер (reseller)
- **Разрешения**: Управление клиентами, продажи, биллинг
- **Описание**: Управление клиентами и продажами
- **Пользователи**: Партнеры-реселлеры

#### ⚙️ Администратор (admin)
- **Разрешения**: Административные функции в рамках тенанта
- **Описание**: Полное управление в рамках организации
- **Пользователи**: IT-администраторы

#### 👁️ Поддержка (support)
- **Разрешения**: Техническая поддержка, просмотр данных
- **Описание**: Техническая поддержка клиентов
- **Пользователи**: Сотрудники поддержки

### Категории разрешений

#### 👥 Клиенты
- `clients.read` - Просмотр клиентов
- `clients.create` - Создание клиентов
- `clients.update` - Редактирование клиентов
- `clients.delete` - Удаление клиентов

#### 📞 SIP
- `sip_accounts.read` - Просмотр SIP аккаунтов
- `sip_accounts.create` - Создание SIP аккаунтов
- `sip_accounts.update` - Редактирование SIP аккаунтов
- `sip_accounts.delete` - Удаление SIP аккаунтов

#### 📱 DID
- `did_numbers.read` - Просмотр DID номеров
- `did_numbers.create` - Создание DID номеров
- `did_numbers.update` - Редактирование DID номеров
- `did_numbers.delete` - Удаление DID номеров
- `did_numbers.assign` - Назначение номеров

#### 👤 Пользователи
- `users.read` - Просмотр пользователей
- `users.create` - Создание пользователей
- `users.update` - Редактирование пользователей
- `users.delete` - Удаление пользователей

#### 🎫 Поддержка
- `tickets.read` - Просмотр тикетов
- `tickets.create` - Создание тикетов
- `tickets.update` - Обновление тикетов
- `tickets.delete` - Удаление тикетов

#### 💰 Биллинг
- `billing.read` - Просмотр биллинга
- `billing.create` - Создание счетов
- `billing.update` - Обновление биллинга

#### 📊 Отчеты
- `reports.read` - Просмотр отчетов
- `reports.create` - Создание отчетов

#### ⚙️ Настройки
- `settings.read` - Просмотр настроек
- `settings.update` - Изменение настроек

#### 📋 Логи
- `logs.read` - Просмотр логов активности

## 🎨 UX/UI особенности

### Создание ролей
- **Валидация полей** в реальном времени
- **Поиск разрешений** по названию и описанию
- **Групповое управление** разрешениями по категориям
- **Счетчик выбранных** разрешений

### Просмотр ролей
- **Иконки ролей** для быстрой идентификации
- **Цветовая индикация** системных/пользовательских ролей
- **Статистика использования** ролей
- **Защита системных ролей** от изменений

### Детали ролей
- **Полная информация** о роли и разрешениях
- **Группировка разрешений** по категориям
- **Предупреждения** для системных ролей
- **Статистика использования**

## 🚀 Использование

1. **Создание роли**:
   - Перейдите в "Роли"
   - Нажмите "Создать роль"
   - Заполните информацию
   - Выберите разрешения
   - Сохраните роль

2. **Редактирование роли**:
   - Найдите роль в списке
   - Нажмите "Редактировать"
   - Измените настройки
   - Сохраните изменения

3. **Просмотр деталей**:
   - Нажмите на иконку "Просмотр"
   - Изучите разрешения роли
   - Проверьте статистику

## 🔒 Безопасность

- **Системные роли** защищены от изменений
- **Валидация разрешений** на уровне интерфейса
- **Аудит изменений** ролей (в логах)
- **Контроль доступа** к управлению ролями

## 🔮 Будущие улучшения

- Наследование ролей
- Временные разрешения
- Условные разрешения
- Интеграция с внешними системами авторизации
- Массовое назначение ролей
