# 📞 Краткое руководство по звонкам

## 🎯 Обзор

Страница "Звонки" предоставляет полнофункциональную систему WebRTC звонков с интеграцией SignalWire для совершения голосовых и видеозвонков прямо из браузера.

## 🚀 Быстрый старт

### 1. Доступ к странице
- Откройте боковое меню
- Нажмите **"📞 Звонки"**
- Страница доступна всем ролям пользователей

### 2. Первоначальная настройка
1. Нажмите **"⚙️ Настройки"** в правом верхнем углу
2. Заполните данные SignalWire:
   - **Project ID**: ваш идентификатор проекта
   - **Auth Token**: токен авторизации
   - **Space URL**: адрес вашего пространства
3. Настройте устройства (микрофон, динамики, камера)
4. Протестируйте подключение
5. Сохраните настройки

## 📱 Основные функции

### 🔢 Набор номера
**Как позвонить:**
1. Перейдите на вкладку **"📞 Набор номера"**
2. Введите номер телефона:
   - Вручную в поле ввода
   - Используя виртуальную клавиатуру
3. Нажмите **"📞 Позвонить"**
4. Дождитесь соединения

**Виртуальная клавиатура:**
```
[1] [2] [3]
[4] [5] [6] 
[7] [8] [9]
[*] [0] [#]
```

### 📋 История звонков
**Просмотр истории:**
1. Перейдите на вкладку **"📋 История"**
2. Используйте поиск для фильтрации
3. Просматривайте детали каждого звонка:
   - Направление (входящий/исходящий)
   - Длительность разговора
   - Статус (завершен, пропущен, занято)
   - Время звонка

**Статусы звонков:**
- **✅ Завершен** - успешный разговор
- **❌ Пропущен** - не отвеченный звонок
- **🟡 Занято** - абонент был занят
- **❌ Ошибка** - техническая проблема

### 👥 Контакты
**Управление контактами:**
1. Перейдите на вкладку **"👥 Контакты"**
2. Просматривайте список контактов
3. Используйте поиск по имени, номеру или компании
4. Нажмите **"📞 Позвонить"** для быстрого набора

**Типы контактов:**
- **Клиенты** - ваши клиенты
- **Внутренние** - сотрудники компании
- **Внешние** - другие контакты

## 🎮 Управление звонком

### Во время звонка доступны:

#### 🎤 Управление звуком
- **Отключить микрофон** - временно выключить звук
- **Включить динамик** - переключить на громкую связь

#### 📹 Управление видео
- **Включить камеру** - начать видеозвонок
- **Отключить видео** - выключить камеру

#### 📞 Завершение
- **Завершить звонок** - закончить разговор

### Интерфейс активного звонка:
```
┌─────────────────────────────────────┐
│          [👤 Аватар]                │
│       Имя контакта                  │
│      +1234567890                    │
│    Исходящий звонок                 │
│                                     │
│         02:45                       │
│                                     │
│ [🎤] [🔊] [📹] [📞]                 │
│ Мут  Дин  Вид  Завершить           │
└─────────────────────────────────────┘
```

## ⚙️ Настройки WebRTC

### 📡 Подключение SignalWire
**Обязательные параметры:**
- **Project ID**: получите в SignalWire Console
- **Auth Token**: токен для авторизации
- **Space URL**: ваш домен SignalWire

### 🎤 Настройки аудио
**Устройства:**
- Выберите микрофон из списка
- Выберите динамики/наушники

**Фильтры звука:**
- ☑️ **Подавление эха** - убирает эхо
- ☑️ **Подавление шума** - фильтрует фоновый шум
- ☑️ **Автоматическая регулировка** - оптимизирует громкость

### 📹 Настройки видео
**Включение видео:**
- ☑️ Включить видеозвонки

**Параметры видео:**
- **Камера**: выбор устройства
- **Разрешение**: 480p, 720p, 1080p
- **Частота кадров**: 15, 24, 30, 60 FPS

### 🛡️ Сетевые настройки
- **Пропускная способность**: ограничение трафика
- **Аудио кодек**: Opus (рекомендуется), G.711, G.722

### 🧪 Тестирование
Перед использованием протестируйте:
1. **Микрофон** - проверка записи звука
2. **Камера** - проверка видео (если включено)
3. **Подключение** - связь с SignalWire

## 🔒 Безопасность и приватность

### Шифрование
- Все звонки защищены **DTLS/SRTP** шифрованием
- Сигнальный канал использует **WSS** (WebSocket Secure)
- Медиа-трафик не проходит через сторонние серверы

### Разрешения браузера
**Необходимые разрешения:**
- 🎤 **Микрофон** - для голосовых звонков
- 📹 **Камера** - для видеозвонков (опционально)
- 🔔 **Уведомления** - для входящих звонков

### Хранение данных
- Настройки сохраняются **локально** в браузере
- Токены **маскируются** в интерфейсе
- История звонков хранится в **зашифрованном** виде

## 🌐 Требования браузера

### Поддерживаемые браузеры:
- ✅ **Chrome 80+** (рекомендуется)
- ✅ **Firefox 75+**
- ✅ **Safari 13+**
- ✅ **Edge 80+**

### Системные требования:
- **HTTPS** соединение (обязательно)
- **Стабильное** интернет-соединение
- **Микрофон** и **динамики/наушники**
- **Камера** (для видеозвонков)

## 🆘 Устранение проблем

### Не могу подключиться
1. ✅ Проверьте учетные данные SignalWire
2. ✅ Убедитесь что сайт открыт по HTTPS
3. ✅ Проверьте интернет-соединение
4. ✅ Перезагрузите страницу

### Проблемы со звуком
1. ✅ Разрешите доступ к микрофону в браузере
2. ✅ Проверьте выбранные устройства в настройках
3. ✅ Протестируйте микрофон в настройках
4. ✅ Проверьте громкость системы

### Проблемы с видео
1. ✅ Включите видеозвонки в настройках
2. ✅ Разрешите доступ к камере в браузере
3. ✅ Выберите правильную камеру
4. ✅ Проверьте освещение

### Плохое качество связи
1. ✅ Проверьте скорость интернета
2. ✅ Уменьшите разрешение видео
3. ✅ Закройте другие приложения
4. ✅ Используйте проводное соединение

## 💡 Полезные советы

### Для лучшего качества:
- 🎧 **Используйте наушники** - избегайте эха
- 🌐 **Стабильный интернет** - минимум 1 Мбит/с
- 💡 **Хорошее освещение** - для видеозвонков
- 🔇 **Тихое место** - минимум фонового шума

### Горячие клавиши:
- **Space** - отключить/включить микрофон
- **V** - включить/выключить видео
- **H** - завершить звонок
- **S** - включить/выключить динамик

### Экономия трафика:
- Отключите видео для голосовых звонков
- Используйте кодек Opus
- Ограничьте пропускную способность

## 📞 Поддержка

При возникновении проблем:
1. Проверьте статус подключения (🟢/🔴 индикатор)
2. Откройте консоль браузера (F12) для диагностики
3. Обратитесь в техническую поддержку через тикет-систему
4. Приложите скриншоты ошибок

**Контакты:**
- 📧 Email: <EMAIL>
- 🎫 Тикеты: раздел "Тикеты" в системе
- 📞 Телефон: +7 (xxx) xxx-xx-xx

---

**🎉 Готово!** Теперь вы можете совершать профессиональные звонки прямо из браузера с помощью технологии WebRTC и SignalWire!
