{"name": "spaas-platform", "version": "1.0.0", "description": "Многоарендаторная SPaaS платформа на базе Supabase", "private": true, "scripts": {"setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh", "dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "preview": "cd frontend && npm run preview", "lint": "cd frontend && npm run lint", "db:migrate": "echo 'Выполните scripts/migrate.sql в Supabase SQL Editor'", "db:seed": "echo 'Выполните SELECT create_demo_data(); в Supabase SQL Editor'", "docs:serve": "echo 'Документация доступна в папке docs/'", "clean": "rm -rf frontend/dist frontend/node_modules", "install:frontend": "cd frontend && npm install"}, "keywords": ["spaas", "supabase", "multi-tenant", "sip", "voip", "platform"], "author": "SPaaS Platform Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["frontend"]}