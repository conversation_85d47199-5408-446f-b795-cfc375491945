import React, { useState, useEffect } from 'react'
import { Zap, Eye, EyeOff, Key, Globe, Shield, Check, AlertCircle, ExternalLink } from 'lucide-react'
import { <PERSON><PERSON>, ModalFooter } from '@/components/Modal'

interface Integration {
  id: string
  name: string
  display_name: string
  description: string
  category: 'telephony' | 'messaging' | 'payment'
  status: 'connected' | 'disconnected' | 'error' | 'pending'
  icon: string
  color: string
  website: string
  features: string[]
  config?: {
    [key: string]: any
  }
}

interface IntegrationConfigModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (integration: Integration, config: any) => void
  integration: Integration | null
}

// Конфигурации для каждой интеграции
const integrationConfigs = {
  twilio: {
    fields: [
      { key: 'account_sid', label: 'Account SID', type: 'text', required: true, placeholder: 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'auth_token', label: 'Auth Token', type: 'password', required: true, placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'phone_number', label: 'Номер телефона', type: 'tel', required: false, placeholder: '+**********' },
      { key: 'webhook_url', label: 'Webhook URL', type: 'url', required: false, placeholder: 'https://api.spaas.com/webhooks/twilio' }
    ],
    docs: 'https://www.twilio.com/docs/usage/api',
    testEndpoint: '/api/integrations/twilio/test'
  },
  signalwire: {
    fields: [
      { key: 'project_id', label: 'Project ID', type: 'text', required: true, placeholder: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' },
      { key: 'auth_token', label: 'Auth Token', type: 'password', required: true, placeholder: 'PTxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'space_url', label: 'Space URL', type: 'url', required: true, placeholder: 'example.signalwire.com' },
      { key: 'webhook_url', label: 'Webhook URL', type: 'url', required: false, placeholder: 'https://api.spaas.com/webhooks/signalwire' }
    ],
    docs: 'https://docs.signalwire.com/',
    testEndpoint: '/api/integrations/signalwire/test'
  },
  viber: {
    fields: [
      { key: 'auth_token', label: 'Auth Token', type: 'password', required: true, placeholder: '4xx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'bot_name', label: 'Имя бота', type: 'text', required: true, placeholder: 'SPaaS Support' },
      { key: 'webhook_url', label: 'Webhook URL', type: 'url', required: false, placeholder: 'https://api.spaas.com/webhooks/viber' }
    ],
    docs: 'https://developers.viber.com/docs/api/',
    testEndpoint: '/api/integrations/viber/test'
  },
  telegram: {
    fields: [
      { key: 'bot_token', label: 'Bot Token', type: 'password', required: true, placeholder: '123456789:ABCdefGHIjklMNOpqrsTUVwxyz' },
      { key: 'bot_username', label: 'Username бота', type: 'text', required: false, placeholder: '@spaas_support_bot' },
      { key: 'webhook_url', label: 'Webhook URL', type: 'url', required: false, placeholder: 'https://api.spaas.com/webhooks/telegram' }
    ],
    docs: 'https://core.telegram.org/bots/api',
    testEndpoint: '/api/integrations/telegram/test'
  },
  stripe: {
    fields: [
      { key: 'publishable_key', label: 'Publishable Key', type: 'text', required: true, placeholder: 'pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'secret_key', label: 'Secret Key', type: 'password', required: true, placeholder: 'sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'webhook_secret', label: 'Webhook Secret', type: 'password', required: false, placeholder: 'whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'webhook_url', label: 'Webhook URL', type: 'url', required: false, placeholder: 'https://api.spaas.com/webhooks/stripe' }
    ],
    docs: 'https://stripe.com/docs/api',
    testEndpoint: '/api/integrations/stripe/test'
  },
  square: {
    fields: [
      { key: 'application_id', label: 'Application ID', type: 'text', required: true, placeholder: 'sq0idp-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'access_token', label: 'Access Token', type: 'password', required: true, placeholder: 'EAAAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' },
      { key: 'environment', label: 'Среда', type: 'select', required: true, options: [
        { value: 'sandbox', label: 'Sandbox (тестовая)' },
        { value: 'production', label: 'Production (боевая)' }
      ]},
      { key: 'webhook_signature_key', label: 'Webhook Signature Key', type: 'password', required: false, placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' }
    ],
    docs: 'https://developer.squareup.com/docs',
    testEndpoint: '/api/integrations/square/test'
  }
}

export const IntegrationConfigModal: React.FC<IntegrationConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  integration
}) => {
  const [config, setConfig] = useState<any>({})
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({})
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)

  useEffect(() => {
    if (integration) {
      setConfig(integration.config || {})
      setShowPasswords({})
      setTestResult(null)
    }
  }, [integration, isOpen])

  if (!integration) return null

  const integrationConfig = integrationConfigs[integration.name as keyof typeof integrationConfigs]
  
  if (!integrationConfig) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} title="Настройка интеграции">
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Конфигурация для этой интеграции пока не доступна</p>
        </div>
      </Modal>
    )
  }

  const handleFieldChange = (key: string, value: string) => {
    setConfig((prev: any) => ({
      ...prev,
      [key]: value
    }))
  }

  const togglePasswordVisibility = (key: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  const handleTest = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      // Симуляция тестирования подключения
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // В реальном приложении здесь будет запрос к API
      const success = Math.random() > 0.3 // 70% успеха для демо
      
      setTestResult({
        success,
        message: success 
          ? 'Подключение успешно установлено!' 
          : 'Ошибка подключения. Проверьте настройки.'
      })
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Ошибка при тестировании подключения'
      })
    } finally {
      setTesting(false)
    }
  }

  const handleSave = () => {
    const requiredFields = integrationConfig.fields.filter(field => field.required)
    const missingFields = requiredFields.filter(field => !config[field.key])

    if (missingFields.length > 0) {
      alert(`Заполните обязательные поля: ${missingFields.map(f => f.label).join(', ')}`)
      return
    }

    onSave(integration, config)
    onClose()
  }

  const renderField = (field: any) => {
    const value = config[field.key] || ''

    switch (field.type) {
      case 'password':
        return (
          <div className="relative">
            <input
              type={showPasswords[field.key] ? 'text' : 'password'}
              value={value}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
              className="form-input pr-10"
              placeholder={field.placeholder}
              required={field.required}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility(field.key)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPasswords[field.key] ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
        )

      case 'select':
        return (
          <select
            value={value}
            onChange={(e) => handleFieldChange(field.key, e.target.value)}
            className="form-input"
            required={field.required}
          >
            <option value="">Выберите...</option>
            {field.options?.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )

      default:
        return (
          <input
            type={field.type}
            value={value}
            onChange={(e) => handleFieldChange(field.key, e.target.value)}
            className="form-input"
            placeholder={field.placeholder}
            required={field.required}
          />
        )
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Настройка ${integration.display_name}`}
      size="lg"
    >
      <div className="space-y-6">
        {/* Информация об интеграции */}
        <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
          <div className={`w-12 h-12 ${integration.color} rounded-lg flex items-center justify-center text-white text-xl`}>
            {integration.icon}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{integration.display_name}</h3>
            <p className="text-sm text-gray-600 mt-1">{integration.description}</p>
            <div className="flex items-center mt-2 space-x-4">
              <a
                href={integrationConfig.docs}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Документация
              </a>
            </div>
          </div>
        </div>

        {/* Поля конфигурации */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center">
            <Key className="h-4 w-4 mr-2" />
            Параметры подключения
          </h4>
          
          {integrationConfig.fields.map((field) => (
            <div key={field.key} className="form-group">
              <label className={`form-label ${field.required ? 'required' : ''}`}>
                {field.label}
              </label>
              {renderField(field)}
              {field.required && (
                <p className="text-xs text-gray-500 mt-1">Обязательное поле</p>
              )}
            </div>
          ))}
        </div>

        {/* Тестирование подключения */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900 flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              Тестирование подключения
            </h4>
            <button
              onClick={handleTest}
              disabled={testing}
              className="btn btn-secondary"
            >
              {testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Тестирование...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Тестировать
                </>
              )}
            </button>
          </div>

          {testResult && (
            <div className={`p-3 rounded-lg flex items-center ${
              testResult.success 
                ? 'bg-green-50 text-green-800' 
                : 'bg-red-50 text-red-800'
            }`}>
              {testResult.success ? (
                <Check className="h-4 w-4 mr-2" />
              ) : (
                <AlertCircle className="h-4 w-4 mr-2" />
              )}
              {testResult.message}
            </div>
          )}
        </div>

        {/* Безопасность */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <Shield className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <div className="font-medium text-yellow-900">
                Безопасность данных
              </div>
              <div className="text-sm text-yellow-700 mt-1">
                Все конфиденциальные данные шифруются и хранятся в безопасном хранилище. 
                Мы рекомендуем использовать отдельные API ключи для интеграций.
              </div>
            </div>
          </div>
        </div>
      </div>

      <ModalFooter>
        <button
          type="button"
          onClick={onClose}
          className="btn btn-secondary"
        >
          Отмена
        </button>
        <button
          onClick={handleSave}
          className="btn btn-primary"
        >
          <Zap className="h-4 w-4 mr-2" />
          Сохранить
        </button>
      </ModalFooter>
    </Modal>
  )
}
