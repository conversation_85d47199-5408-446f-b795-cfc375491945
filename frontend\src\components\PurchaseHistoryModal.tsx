import React, { useState, useEffect } from 'react'
import { Calendar, User, Phone, DollarSign, FileText, Download, Filter } from 'lucide-react'
import { <PERSON><PERSON>, ModalFooter } from '@/components/Modal'
import { formatCurrency, formatDate } from '@/lib/utils'

interface PurchaseRecord {
  purchase_id: string
  numbers: string[]
  purchased_by: string
  purchased_by_user: {
    first_name: string
    last_name: string
    email: string
  }
  purchased_at: string
  total_setup_cost: number
  total_monthly_cost: number
  notes?: string
  provider: string
  status: 'completed' | 'pending' | 'failed'
}

interface PurchaseHistoryModalProps {
  isOpen: boolean
  onClose: () => void
}

// Демо данные истории покупок
const purchaseHistory: PurchaseRecord[] = [
  {
    purchase_id: 'PUR-001',
    numbers: ['+****************', '+****************'],
    purchased_by: 'user-123',
    purchased_by_user: {
      first_name: '<PERSON>ван',
      last_name: 'Петров',
      email: '<EMAIL>'
    },
    purchased_at: '2024-01-15T10:30:00Z',
    total_setup_cost: 1500,
    total_monthly_cost: 3000,
    notes: 'Номера для отдела продаж',
    provider: 'Twilio',
    status: 'completed'
  },
  {
    purchase_id: 'PUR-002',
    numbers: ['+7 (495) 123-45-67'],
    purchased_by: 'user-456',
    purchased_by_user: {
      first_name: 'Мария',
      last_name: 'Сидорова',
      email: '<EMAIL>'
    },
    purchased_at: '2024-01-10T14:20:00Z',
    total_setup_cost: 1000,
    total_monthly_cost: 2500,
    notes: 'Московский номер для поддержки',
    provider: 'Rostelecom',
    status: 'completed'
  },
  {
    purchase_id: 'PUR-003',
    numbers: ['+****************'],
    purchased_by: 'user-789',
    purchased_by_user: {
      first_name: 'Алексей',
      last_name: 'Козлов',
      email: '<EMAIL>'
    },
    purchased_at: '2024-01-08T09:15:00Z',
    total_setup_cost: 1500,
    total_monthly_cost: 3000,
    notes: 'Toll-free номер для клиентской поддержки',
    provider: 'Bandwidth',
    status: 'completed'
  },
  {
    purchase_id: 'PUR-004',
    numbers: ['+44 20 7946 0958', '+44 20 7946 0959'],
    purchased_by: 'user-123',
    purchased_by_user: {
      first_name: 'Иван',
      last_name: 'Петров',
      email: '<EMAIL>'
    },
    purchased_at: '2024-01-05T16:45:00Z',
    total_setup_cost: 2400,
    total_monthly_cost: 5600,
    notes: 'Лондонские номера для европейского офиса',
    provider: 'Vonage',
    status: 'pending'
  }
]

export const PurchaseHistoryModal: React.FC<PurchaseHistoryModalProps> = ({
  isOpen,
  onClose
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('all')

  const filteredHistory = purchaseHistory.filter(record => {
    const matchesSearch = 
      record.numbers.some(num => num.includes(searchTerm)) ||
      record.purchased_by_user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.purchased_by_user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.purchased_by_user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.provider.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (record.notes && record.notes.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesStatus = statusFilter === 'all' || record.status === statusFilter

    let matchesDate = true
    if (dateFilter !== 'all') {
      const recordDate = new Date(record.purchased_at)
      const now = new Date()
      
      switch (dateFilter) {
        case 'today':
          matchesDate = recordDate.toDateString() === now.toDateString()
          break
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          matchesDate = recordDate >= weekAgo
          break
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          matchesDate = recordDate >= monthAgo
          break
      }
    }

    return matchesSearch && matchesStatus && matchesDate
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: 'Завершено', className: 'badge-success' },
      pending: { label: 'В обработке', className: 'badge-warning' },
      failed: { label: 'Ошибка', className: 'badge-danger' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.completed
    
    return (
      <span className={`badge ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const getTotalStats = () => {
    const totalRecords = filteredHistory.length
    const totalNumbers = filteredHistory.reduce((sum, record) => sum + record.numbers.length, 0)
    const totalSetupCost = filteredHistory.reduce((sum, record) => sum + record.total_setup_cost, 0)
    const totalMonthlyCost = filteredHistory.reduce((sum, record) => sum + record.total_monthly_cost, 0)

    return { totalRecords, totalNumbers, totalSetupCost, totalMonthlyCost }
  }

  const exportToCSV = () => {
    const headers = [
      'ID покупки',
      'Номера',
      'Покупатель',
      'Email',
      'Дата покупки',
      'Стоимость подключения',
      'Ежемесячная стоимость',
      'Провайдер',
      'Статус',
      'Примечания'
    ]

    const csvData = filteredHistory.map(record => [
      record.purchase_id,
      record.numbers.join('; '),
      `${record.purchased_by_user.first_name} ${record.purchased_by_user.last_name}`,
      record.purchased_by_user.email,
      formatDate(record.purchased_at),
      record.total_setup_cost,
      record.total_monthly_cost,
      record.provider,
      record.status,
      record.notes || ''
    ])

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `purchase_history_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const stats = getTotalStats()

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="История покупок DID номеров"
      size="xl"
    >
      <div className="space-y-6">
        {/* Статистика */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.totalRecords}</div>
            <div className="text-sm text-blue-600">Покупок</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{stats.totalNumbers}</div>
            <div className="text-sm text-green-600">Номеров</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(stats.totalSetupCost)}
            </div>
            <div className="text-sm text-purple-600">Подключение</div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {formatCurrency(stats.totalMonthlyCost)}
            </div>
            <div className="text-sm text-orange-600">В месяц</div>
          </div>
        </div>

        {/* Фильтры */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="search-box">
              <Filter className="search-icon" />
              <input
                type="text"
                placeholder="Поиск по номерам, пользователям, провайдерам..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          <div className="sm:w-40">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-input"
            >
              <option value="all">Все статусы</option>
              <option value="completed">Завершено</option>
              <option value="pending">В обработке</option>
              <option value="failed">Ошибка</option>
            </select>
          </div>

          <div className="sm:w-40">
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="form-input"
            >
              <option value="all">Все даты</option>
              <option value="today">Сегодня</option>
              <option value="week">Неделя</option>
              <option value="month">Месяц</option>
            </select>
          </div>

          <button
            onClick={exportToCSV}
            className="btn btn-secondary"
          >
            <Download className="h-4 w-4 mr-2" />
            Экспорт
          </button>
        </div>

        {/* Таблица истории */}
        <div className="max-h-96 overflow-y-auto border rounded-lg">
          <table className="table">
            <thead className="sticky top-0 bg-gray-50">
              <tr>
                <th>ID</th>
                <th>Номера</th>
                <th>Покупатель</th>
                <th>Дата</th>
                <th>Стоимость</th>
                <th>Провайдер</th>
                <th>Статус</th>
                <th>Примечания</th>
              </tr>
            </thead>
            <tbody>
              {filteredHistory.map((record) => (
                <tr key={record.purchase_id} className="hover:bg-gray-50">
                  <td>
                    <div className="font-mono text-sm text-blue-600">
                      {record.purchase_id}
                    </div>
                  </td>
                  <td>
                    <div className="space-y-1">
                      {record.numbers.map((number, index) => (
                        <div key={index} className="flex items-center text-sm">
                          <Phone className="h-3 w-3 text-gray-400 mr-1" />
                          {number}
                        </div>
                      ))}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {record.purchased_by_user.first_name} {record.purchased_by_user.last_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {record.purchased_by_user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      {formatDate(record.purchased_at)}
                    </div>
                  </td>
                  <td>
                    <div className="text-sm">
                      <div className="flex items-center">
                        <DollarSign className="h-3 w-3 text-gray-400 mr-1" />
                        {formatCurrency(record.total_setup_cost)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatCurrency(record.total_monthly_cost)}/мес
                      </div>
                    </div>
                  </td>
                  <td className="text-sm text-gray-600">
                    {record.provider}
                  </td>
                  <td>
                    {getStatusBadge(record.status)}
                  </td>
                  <td>
                    {record.notes && (
                      <div className="flex items-center text-sm text-gray-600">
                        <FileText className="h-3 w-3 mr-1" />
                        <span className="truncate max-w-32" title={record.notes}>
                          {record.notes}
                        </span>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredHistory.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                История покупок не найдена по заданным критериям
              </div>
            </div>
          )}
        </div>
      </div>

      <ModalFooter>
        <button
          type="button"
          onClick={onClose}
          className="btn btn-secondary"
        >
          Закрыть
        </button>
      </ModalFooter>
    </Modal>
  )
}
