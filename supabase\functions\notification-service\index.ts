import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface NotificationRequest {
  type: 'email' | 'sms' | 'webhook'
  template: string
  recipient: string
  data: Record<string, any>
  tenant_id: string
}

interface EmailTemplate {
  subject: string
  html: string
  text: string
}

const EMAIL_TEMPLATES: Record<string, EmailTemplate> = {
  'invoice_created': {
    subject: 'Новый инвойс #{invoice_number}',
    html: `
      <h2>Уважаемый {client_name}!</h2>
      <p>Для вас выставлен новый инвойс #{invoice_number} на сумму {total_amount}.</p>
      <p><strong>Срок оплаты:</strong> {due_date}</p>
      <p><strong>Сумма к оплате:</strong> {total_amount}</p>
      <p>Для оплаты перейдите в личный кабинет или свяжитесь с нами.</p>
      <hr>
      <p>С уважением,<br>Команда SPaaS Platform</p>
    `,
    text: `
      Уважаемый {client_name}!
      
      Для вас выставлен новый инвойс #{invoice_number} на сумму {total_amount}.
      Срок оплаты: {due_date}
      
      Для оплаты перейдите в личный кабинет или свяжитесь с нами.
      
      С уважением,
      Команда SPaaS Platform
    `
  },
  'payment_received': {
    subject: 'Платеж получен - {payment_number}',
    html: `
      <h2>Уважаемый {client_name}!</h2>
      <p>Мы получили ваш платеж {payment_number} на сумму {amount}.</p>
      <p><strong>Дата платежа:</strong> {payment_date}</p>
      <p><strong>Способ оплаты:</strong> {payment_method}</p>
      <p>Спасибо за своевременную оплату!</p>
      <hr>
      <p>С уважением,<br>Команда SPaaS Platform</p>
    `,
    text: `
      Уважаемый {client_name}!
      
      Мы получили ваш платеж {payment_number} на сумму {amount}.
      Дата платежа: {payment_date}
      Способ оплаты: {payment_method}
      
      Спасибо за своевременную оплату!
      
      С уважением,
      Команда SPaaS Platform
    `
  },
  'ticket_created': {
    subject: 'Новый тикет #{ticket_number}',
    html: `
      <h2>Уважаемый {client_name}!</h2>
      <p>Создан новый тикет #{ticket_number}.</p>
      <p><strong>Тема:</strong> {subject}</p>
      <p><strong>Приоритет:</strong> {priority}</p>
      <p><strong>Описание:</strong></p>
      <p>{description}</p>
      <p>Мы свяжемся с вами в ближайшее время.</p>
      <hr>
      <p>С уважением,<br>Команда SPaaS Platform</p>
    `,
    text: `
      Уважаемый {client_name}!
      
      Создан новый тикет #{ticket_number}.
      Тема: {subject}
      Приоритет: {priority}
      
      Описание:
      {description}
      
      Мы свяжемся с вами в ближайшее время.
      
      С уважением,
      Команда SPaaS Platform
    `
  }
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { type, template, recipient, data, tenant_id } = await req.json() as NotificationRequest

    // Проверяем авторизацию
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    switch (type) {
      case 'email':
        return await sendEmail(supabaseClient, template, recipient, data, tenant_id)
      
      case 'sms':
        return await sendSMS(supabaseClient, template, recipient, data, tenant_id)
      
      case 'webhook':
        return await sendWebhook(supabaseClient, template, recipient, data, tenant_id)
      
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid notification type' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }

  } catch (error) {
    console.error('Notification service error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function sendEmail(supabaseClient: any, template: string, recipient: string, data: Record<string, any>, tenant_id: string) {
  try {
    const emailTemplate = EMAIL_TEMPLATES[template]
    if (!emailTemplate) {
      return new Response(
        JSON.stringify({ error: 'Email template not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Заменяем переменные в шаблоне
    let subject = emailTemplate.subject
    let html = emailTemplate.html
    let text = emailTemplate.text

    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{${key}}`
      subject = subject.replace(new RegExp(placeholder, 'g'), String(value))
      html = html.replace(new RegExp(placeholder, 'g'), String(value))
      text = text.replace(new RegExp(placeholder, 'g'), String(value))
    }

    // В реальной системе здесь будет отправка через SMTP или email сервис
    console.log('Sending email:', {
      to: recipient,
      subject,
      html,
      text
    })

    // Логируем отправку уведомления
    await supabaseClient
      .from('activity_logs')
      .insert({
        tenant_id,
        user_id: null,
        action: 'SEND_EMAIL',
        table_name: 'notifications',
        record_id: null,
        changes: {
          template,
          recipient,
          subject
        }
      })

    // В демо режиме просто возвращаем успех
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email sent successfully',
        notification_id: `email_${Date.now()}`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Send email error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to send email' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function sendSMS(supabaseClient: any, template: string, recipient: string, data: Record<string, any>, tenant_id: string) {
  try {
    // SMS шаблоны (упрощенные)
    const smsTemplates: Record<string, string> = {
      'payment_received': 'Платеж {amount} получен. Спасибо! SPaaS Platform',
      'invoice_overdue': 'Инвойс {invoice_number} просрочен. Сумма: {total_amount}. Оплатите в ЛК.',
      'service_suspended': 'Услуги приостановлены из-за неоплаты. Обратитесь в поддержку.'
    }

    let message = smsTemplates[template] || 'Уведомление от SPaaS Platform'
    
    // Заменяем переменные
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{${key}}`
      message = message.replace(new RegExp(placeholder, 'g'), String(value))
    }

    // В реальной системе здесь будет отправка через SMS API
    console.log('Sending SMS:', {
      to: recipient,
      message
    })

    // Логируем отправку
    await supabaseClient
      .from('activity_logs')
      .insert({
        tenant_id,
        user_id: null,
        action: 'SEND_SMS',
        table_name: 'notifications',
        record_id: null,
        changes: {
          template,
          recipient,
          message
        }
      })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'SMS sent successfully',
        notification_id: `sms_${Date.now()}`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Send SMS error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to send SMS' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function sendWebhook(supabaseClient: any, template: string, recipient: string, data: Record<string, any>, tenant_id: string) {
  try {
    // Получаем настройки webhook для tenant
    const { data: webhookConfig, error: configError } = await supabaseClient
      .from('tenant_settings')
      .select('webhook_url, webhook_secret')
      .eq('tenant_id', tenant_id)
      .single()

    if (configError || !webhookConfig?.webhook_url) {
      return new Response(
        JSON.stringify({ error: 'Webhook not configured' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const payload = {
      event: template,
      timestamp: new Date().toISOString(),
      tenant_id,
      data
    }

    // Создаем подпись (если настроен secret)
    let signature = ''
    if (webhookConfig.webhook_secret) {
      const encoder = new TextEncoder()
      const keyData = encoder.encode(webhookConfig.webhook_secret)
      const messageData = encoder.encode(JSON.stringify(payload))
      
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'HMAC', hash: 'SHA-256' },
        false,
        ['sign']
      )
      
      const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, messageData)
      signature = Array.from(new Uint8Array(signatureBuffer))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
    }

    // Отправляем webhook
    const webhookResponse = await fetch(webhookConfig.webhook_url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-SPaaS-Signature': signature,
        'X-SPaaS-Event': template
      },
      body: JSON.stringify(payload)
    })

    // Логируем отправку
    await supabaseClient
      .from('activity_logs')
      .insert({
        tenant_id,
        user_id: null,
        action: 'SEND_WEBHOOK',
        table_name: 'notifications',
        record_id: null,
        changes: {
          template,
          webhook_url: webhookConfig.webhook_url,
          status: webhookResponse.status
        }
      })

    return new Response(
      JSON.stringify({
        success: webhookResponse.ok,
        message: webhookResponse.ok ? 'Webhook sent successfully' : 'Webhook failed',
        status: webhookResponse.status,
        notification_id: `webhook_${Date.now()}`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Send webhook error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to send webhook' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}
