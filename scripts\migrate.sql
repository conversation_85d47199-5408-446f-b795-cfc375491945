-- Скрипт для применения всех миграций к существующему проекту Supabase
-- Запустите этот файл в SQL Editor вашего Supabase проекта

-- Включаем необходимые расширения
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Создание таблиц
\echo 'Создание таблиц...'

-- 1. Tenants
CREATE TABLE IF NOT EXISTS tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
    subscription_plan VARCHAR(100),
    max_users INTEGER DEFAULT 10,
    max_clients INTEGER DEFAULT 100,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Функция для обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Триггер для tenants
CREATE TRIGGER update_tenants_updated_at 
    BEFORE UPDATE ON tenants 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 2. Users (расширение auth.users)
CREATE TABLE IF NOT EXISTS users (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL CHECK (role IN ('provider', 'reseller', 'admin', 'support', 'staff', 'client')),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    position VARCHAR(100),
    department VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    settings JSONB DEFAULT '{}',
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 3. Clients
CREATE TABLE IF NOT EXISTS clients (
    client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    client_code VARCHAR(50),
    company_name VARCHAR(255),
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    tax_id VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
    billing_type VARCHAR(50) DEFAULT 'postpaid' CHECK (billing_type IN ('prepaid', 'postpaid')),
    credit_limit DECIMAL(10,2) DEFAULT 0,
    current_balance DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    time_zone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    notes TEXT,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

CREATE TRIGGER update_clients_updated_at
    BEFORE UPDATE ON clients
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 4. SIP Accounts
CREATE TABLE IF NOT EXISTS sip_accounts (
    sip_account_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(client_id) ON DELETE CASCADE,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    display_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
    max_concurrent_calls INTEGER DEFAULT 1,
    codec_preferences JSONB DEFAULT '["G.711", "G.729"]',
    nat_settings JSONB DEFAULT '{}',
    security_settings JSONB DEFAULT '{}',
    call_routing JSONB DEFAULT '{}',
    last_registration TIMESTAMP WITH TIME ZONE,
    registration_expires TIMESTAMP WITH TIME ZONE,
    user_agent VARCHAR(255),
    ip_address INET,
    port INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

CREATE TRIGGER update_sip_accounts_updated_at
    BEFORE UPDATE ON sip_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5. DID Numbers
CREATE TABLE IF NOT EXISTS did_numbers (
    did_number_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(client_id) ON DELETE SET NULL,
    number VARCHAR(20) NOT NULL UNIQUE,
    country_code VARCHAR(5),
    area_code VARCHAR(10),
    number_type VARCHAR(50) DEFAULT 'local' CHECK (number_type IN ('local', 'national', 'international', 'toll_free')),
    status VARCHAR(50) DEFAULT 'available' CHECK (status IN ('available', 'assigned', 'suspended', 'ported_out')),
    monthly_cost DECIMAL(8,2) DEFAULT 0,
    setup_cost DECIMAL(8,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    provider VARCHAR(100),
    provider_id VARCHAR(100),
    routing_settings JSONB DEFAULT '{}',
    features JSONB DEFAULT '{}',
    assigned_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

CREATE TRIGGER update_did_numbers_updated_at
    BEFORE UPDATE ON did_numbers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 6. Tickets
CREATE TABLE IF NOT EXISTS tickets (
    ticket_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(client_id) ON DELETE SET NULL,
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    subject VARCHAR(500) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_client', 'resolved', 'closed')),
    priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    category VARCHAR(100),
    subcategory VARCHAR(100),
    assigned_to UUID REFERENCES auth.users(id),
    created_by UUID REFERENCES auth.users(id),
    resolved_by UUID REFERENCES auth.users(id),
    resolution TEXT,
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    due_date TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    first_response_at TIMESTAMP WITH TIME ZONE,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TRIGGER update_tickets_updated_at
    BEFORE UPDATE ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Функция для генерации номера тикета
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.ticket_number IS NULL THEN
        NEW.ticket_number := 'T' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' ||
                           LPAD(NEXTVAL('ticket_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Создание последовательности для номеров тикетов
CREATE SEQUENCE IF NOT EXISTS ticket_number_seq START 1;

-- Триггер для автоматической генерации номера тикета
CREATE TRIGGER generate_ticket_number_trigger
    BEFORE INSERT ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION generate_ticket_number();

-- 7. Logs
CREATE TABLE IF NOT EXISTS logs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('debug', 'info', 'warning', 'error', 'critical')),
    message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Создание индексов
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_clients_tenant_id ON clients(tenant_id);
CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status);

-- Включение RLS
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE sip_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE did_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE logs ENABLE ROW LEVEL SECURITY;

-- Функции для RLS
CREATE OR REPLACE FUNCTION auth.user_id() 
RETURNS UUID AS $$
  SELECT COALESCE(
    (auth.jwt() ->> 'sub')::UUID,
    NULL
  );
$$ LANGUAGE SQL STABLE;

CREATE OR REPLACE FUNCTION auth.user_role() 
RETURNS TEXT AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'role',
    'anonymous'
  );
$$ LANGUAGE SQL STABLE;

CREATE OR REPLACE FUNCTION auth.user_tenant_id() 
RETURNS UUID AS $$
  SELECT COALESCE(
    (auth.jwt() ->> 'tenant_id')::UUID,
    NULL
  );
$$ LANGUAGE SQL STABLE;

CREATE OR REPLACE FUNCTION auth.is_provider() 
RETURNS BOOLEAN AS $$
  SELECT auth.user_role() = 'provider';
$$ LANGUAGE SQL STABLE;

-- Базовые RLS политики
-- Tenants
CREATE POLICY "Provider full access to tenants" ON tenants
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

CREATE POLICY "Users can view own tenant" ON tenants
  FOR SELECT 
  USING (tenant_id = auth.user_tenant_id());

-- Users  
CREATE POLICY "Provider full access to users" ON users
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

CREATE POLICY "Users can view tenant users" ON users
  FOR SELECT 
  USING (tenant_id = auth.user_tenant_id());

-- Clients
CREATE POLICY "Provider full access to clients" ON clients
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

CREATE POLICY "Tenant users can view clients" ON clients
  FOR SELECT
  USING (tenant_id = auth.user_tenant_id());

-- Дополнительные политики для остальных таблиц
CREATE POLICY "Provider full access to sip_accounts" ON sip_accounts
  FOR ALL
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

CREATE POLICY "Tenant users can view sip_accounts" ON sip_accounts
  FOR SELECT
  USING (tenant_id = auth.user_tenant_id());

CREATE POLICY "Provider full access to did_numbers" ON did_numbers
  FOR ALL
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

CREATE POLICY "Tenant users can view did_numbers" ON did_numbers
  FOR SELECT
  USING (tenant_id = auth.user_tenant_id());

CREATE POLICY "Provider full access to tickets" ON tickets
  FOR ALL
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

CREATE POLICY "Tenant users can view tickets" ON tickets
  FOR SELECT
  USING (tenant_id = auth.user_tenant_id());

CREATE POLICY "Provider can view all logs" ON logs
  FOR SELECT
  USING (auth.is_provider());

CREATE POLICY "Tenant users can view tenant logs" ON logs
  FOR SELECT
  USING (tenant_id = auth.user_tenant_id());

CREATE POLICY "Authenticated users can create logs" ON logs
  FOR INSERT
  WITH CHECK (auth.user_id() IS NOT NULL);

-- Функция для создания демо данных
CREATE OR REPLACE FUNCTION create_demo_data()
RETURNS VOID AS $$
DECLARE
    demo_tenant_id UUID;
    demo_client_id UUID;
BEGIN
    -- Создаем демо арендатора
    INSERT INTO tenants (
        name,
        domain,
        status,
        subscription_plan,
        max_users,
        max_clients
    ) VALUES (
        'Demo Reseller Company',
        'demo.example.com',
        'active',
        'professional',
        50,
        500
    ) RETURNING tenant_id INTO demo_tenant_id;

    -- Создаем демо клиента
    INSERT INTO clients (
        tenant_id,
        client_code,
        company_name,
        contact_person,
        email,
        phone,
        status,
        billing_type
    ) VALUES (
        demo_tenant_id,
        'DEMO001',
        'Demo Client Corp',
        'John Doe',
        '<EMAIL>',
        '+1234567890',
        'active',
        'postpaid'
    ) RETURNING client_id INTO demo_client_id;

    -- Создаем демо тикет
    INSERT INTO tickets (
        tenant_id,
        client_id,
        ticket_number,
        subject,
        description,
        status,
        priority,
        category
    ) VALUES (
        demo_tenant_id,
        demo_client_id,
        'T' || TO_CHAR(NOW(), 'YYYYMMDD') || '-0001',
        'Тестовый тикет',
        'Это демонстрационный тикет для проверки системы',
        'open',
        'medium',
        'support'
    );

    RAISE NOTICE 'Demo data created successfully. Tenant ID: %', demo_tenant_id;
END;
$$ LANGUAGE plpgsql;

\echo 'Миграция завершена успешно!';
