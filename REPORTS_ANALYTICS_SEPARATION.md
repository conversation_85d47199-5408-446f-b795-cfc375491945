# 📊 Разделение отчетов и аналитики

## Обзор изменений

Система отчетов и аналитики разделена на два отдельных раздела с разным функционалом:

1. **📄 Отчеты** - Генерация, управление и экспорт отчетов
2. **📈 Аналитика** - Визуальный анализ данных и трендов

## 📄 Страница "Отчеты"

### Основной функционал:
- **Шаблоны отчетов** - готовые типы отчетов для генерации
- **Управление отчетами** - создание, скачивание, удаление
- **Статусы генерации** - отслеживание процесса создания
- **Фильтрация и поиск** - быстрый поиск нужных отчетов

### Типы отчетов:

#### 💰 Финансовые отчеты
- **Финансовый отчет** - сводка доходов, расходов и прибыли
- **Биллинговый отчет** - инвойсы, платежи, задолженности

#### 👥 Операционные отчеты  
- **Активность клиентов** - статистика использования услуг
- **Отчет по тикетам** - статистика обращений в поддержку

#### ⚙️ Технические отчеты
- **Статистика звонков** - детальная статистика звонков
- **Техническое состояние** - мониторинг системы

### Статусы отчетов:
- **✅ Готов** - отчет сгенерирован и доступен для скачивания
- **🔄 Генерируется** - отчет в процессе создания
- **📅 Запланирован** - отчет запланирован к генерации
- **❌ Ошибка** - произошла ошибка при генерации

### Форматы экспорта:
- **PDF** - для презентаций и печати
- **Excel** - для дальнейшего анализа
- **CSV** - для импорта в другие системы

## 📈 Страница "Аналитика"

### Основной функционал:
- **Интерактивные графики** - визуализация данных
- **KPI метрики** - ключевые показатели эффективности
- **Тренды и динамика** - анализ изменений во времени
- **Сравнительный анализ** - сопоставление показателей

### Типы аналитики:

#### 💰 Финансовая аналитика
- **Динамика выручки** - тренды доходов по месяцам
- **Распределение услуг** - доли разных типов услуг
- **Прибыльность** - анализ рентабельности

#### 👥 Клиентская аналитика
- **Рост клиентской базы** - новые vs ушедшие клиенты
- **Активность клиентов** - использование услуг
- **Сегментация** - группировка по характеристикам

#### 📞 Операционная аналитика
- **Статистика звонков** - входящие/исходящие по дням
- **Качество связи** - показатели производительности
- **Загрузка системы** - пиковые нагрузки

#### 🎫 Аналитика поддержки
- **Метрики тикетов** - время решения, категории
- **Эффективность поддержки** - KPI сотрудников
- **Удовлетворенность** - рейтинги клиентов

## 🎯 Интерфейс

### Навигация в sidebar:
```
📊 Дашборд
👥 Клиенты
👤 Пользователи
🛡️ Роли
📞 SIP Аккаунты
📱 DID Номера
🎫 Тикеты
💰 Биллинг
📄 Отчеты          ← НОВОЕ (FileText icon)
📈 Аналитика       ← НОВОЕ (TrendingUp icon)
📋 Логи
⚡ Интеграции
⚙️ Настройки
```

### Страница "Отчеты":
```
┌─────────────────────────────────────────┐
│ 📄 Отчеты                [➕ Создать]   │
├─────────────────────────────────────────┤
│ ✅ 3 готовых | 🔄 1 генерируется        │
│                                         │
│ 🎯 Шаблоны отчетов                      │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐     │
│ │💰 Финанс│ │👥 Клиент│ │📞 Звонки│     │
│ │ отчет   │ │активност│ │статистик│     │
│ │[Создать]│ │[Создать]│ │[Создать]│     │
│ └─────────┘ └─────────┘ └─────────┘     │
│                                         │
│ 📋 Сгенерированные отчеты               │
│ [Поиск] [Тип▼] [Статус▼]               │
│ ✅ Финансовый отчет за ноябрь [Скачать] │
│ 🔄 Статистика звонков     [Генерируется]│
│ ✅ Активность клиентов    [Скачать]     │
└─────────────────────────────────────────┘
```

### Страница "Аналитика":
```
┌─────────────────────────────────────────┐
│ 📈 Аналитика              [Период▼]     │
├─────────────────────────────────────────┤
│ 💰 165k₽ | 👥 38 | 📞 5.4k | ⏱️ 6мин   │
│                                         │
│ ┌─────────────────┐ ┌─────────────────┐ │
│ │📈 Динамика      │ │👥 Рост клиентов │ │
│ │   выручки       │ │                 │ │
│ │   [График]      │ │   [График]      │ │
│ └─────────────────┘ └─────────────────┘ │
│                                         │
│ ┌─────────────────┐ ┌─────────────────┐ │
│ │📞 Статистика    │ │🥧 Распределение │ │
│ │   звонков       │ │   услуг         │ │
│ │   [График]      │ │   [График]      │ │
│ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
```

## 🔧 Технические детали

### Новые файлы:
- `Reports.tsx` - страница управления отчетами
- `Analytics.tsx` - страница аналитики с графиками

### Обновленные файлы:
- `Sidebar.tsx` - добавлены два отдельных пункта меню
- `App.tsx` - добавлен роут для аналитики

### Компоненты графиков:
- **LineChart** - линейные графики для трендов
- **BarChart** - столбчатые диаграммы для сравнений
- **AreaChart** - области для накопительных данных
- **PieChart** - круговые диаграммы для долей

### Типы данных:

#### Отчет
```typescript
interface Report {
  report_id: string
  name: string
  description: string
  type: 'financial' | 'operational' | 'technical' | 'custom'
  format: 'pdf' | 'excel' | 'csv'
  schedule: 'manual' | 'daily' | 'weekly' | 'monthly'
  status: 'ready' | 'generating' | 'scheduled' | 'error'
  created_at: string
  generated_at?: string
  file_size?: number
  parameters: {
    date_from: string
    date_to: string
    filters?: any
  }
}
```

#### Шаблон отчета
```typescript
interface ReportTemplate {
  template_id: string
  name: string
  description: string
  type: 'financial' | 'operational' | 'technical' | 'custom'
  icon: string
  color: string
  fields: string[]
  estimated_time: string
}
```

## 🎨 UX/UI особенности

### Отчеты:
- **Карточки шаблонов** с описанием и временем генерации
- **Статусы в реальном времени** с индикаторами прогресса
- **Фильтрация и поиск** для быстрого поиска
- **Действия с отчетами** - скачивание, удаление

### Аналитика:
- **Интерактивные графики** с подсказками
- **Цветовая кодировка** для разных типов данных
- **Адаптивная сетка** графиков
- **Переключение периодов** и типов просмотра

## 🚀 Использование

### Создание отчета:
1. Перейдите в "Отчеты"
2. Выберите шаблон отчета
3. Нажмите "Создать"
4. Дождитесь генерации
5. Скачайте готовый отчет

### Просмотр аналитики:
1. Перейдите в "Аналитика"
2. Выберите период анализа
3. Изучите графики и метрики
4. Экспортируйте данные при необходимости

## 🔒 Права доступа

Обе страницы доступны для ролей:
- **Provider** - полный доступ
- **Reseller** - полный доступ  
- **Admin** - полный доступ

## 🔮 Будущие улучшения

### Отчеты:
- Планировщик автоматических отчетов
- Настраиваемые шаблоны
- Email-рассылка отчетов
- Версионирование отчетов

### Аналитика:
- Дополнительные типы графиков
- Интерактивные фильтры
- Сравнение периодов
- Прогнозирование трендов
- Экспорт графиков в изображения

## 📱 Доступность

- **Отчеты**: `/reports`
- **Аналитика**: `/analytics`

Обе страницы полностью адаптированы для мобильных устройств и поддерживают все современные браузеры.
