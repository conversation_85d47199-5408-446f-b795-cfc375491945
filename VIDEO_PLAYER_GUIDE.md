# 📹 Руководство по видеоплееру

## 🎯 Обзор

Система звонков теперь включает профессиональные видео и аудио плееры для полноценных WebRTC звонков с автоматическим переключением между режимами.

## 🎥 Типы плееров

### 📹 Видеоплеер (VideoPlayer)
Используется когда:
- ✅ Включено локальное видео (`isVideoOn = true`)
- ✅ Получен удаленный видео поток (`remoteStream`)
- ✅ Участник включил камеру

### 🎵 Аудиоплеер (AudioPlayer)  
Используется когда:
- ❌ Видео отключено у всех участников
- ✅ Только голосовое общение
- ✅ Экономия трафика

## 🎮 Видеоплеер - Функции

### 🖥️ Режимы отображения:

#### 1. 📺 Фокус на собеседнике (по умолчанию)
```
┌─────────────────────────────────────────┐
│ [Большое видео собеседника]             │
│                                         │
│                    ┌─────────┐          │
│                    │Ваше видео│         │
│                    │(маленькое)│        │
│                    └─────────┘          │
│ [🎤] [📹] [🔊] [⛶] [📺] [📞]           │
└─────────────────────────────────────────┘
```

#### 2. 📱 Рядом
```
┌─────────────────────────────────────────┐
│ ┌─────────────────┐ ┌─────────────────┐ │
│ │  Собеседник     │ │    Ваше видео   │ │
│ │                 │ │                 │ │
│ │                 │ │                 │ │
│ └─────────────────┘ └─────────────────┘ │
│ [🎤] [📹] [🔊] [⛶] [📺] [📞]           │
└─────────────────────────────────────────┘
```

#### 3. 🖼️ Картинка в картинке
```
┌─────────────────────────────────────────┐
│ [Большое видео собеседника]             │
│                                         │
│                          ┌───────────┐  │
│                          │Ваше видео │  │
│                          │(большое)  │  │
│                          └───────────┘  │
│ [🎤] [📹] [🔊] [⛶] [📺] [📞]           │
└─────────────────────────────────────────┘
```

### 🎛️ Элементы управления:

#### Основные кнопки:
- **🎤 Микрофон** - включить/выключить звук
  - Зеленый = включен, Красный = выключен
- **📹 Видео** - включить/выключить камеру
  - Зеленый = включено, Красный = выключено
- **🔊 Динамики** - управление звуком собеседника
  - Синий = включен, Желтый = выключен
- **⛶ Полноэкранный** - развернуть на весь экран
- **📺 PiP** - режим картинка-в-картинке
- **📞 Завершить** - закончить звонок (красная кнопка)

#### Дополнительные функции:
- **Изменение размера** локального видео (клик по иконке в углу)
- **Переключение режимов** (кнопки сверху)
- **Автоскрытие** элементов в полноэкранном режиме (через 3 сек)

### 📊 Индикаторы:
- **Таймер звонка** - в центре сверху
- **Имена участников** - на видео
- **Статус микрофона** - иконки на видео
- **Качество связи** - "HD" индикатор
- **Статус подключения** - зеленая точка

## 🎵 Аудиоплеер - Функции

### 👤 Интерфейс участника:
```
┌─────────────────────────────────────────┐
│              [👤 Аватар]                │
│           (пульсирует при звуке)        │
│                                         │
│           Иван Петров                   │
│          +1987654321                    │
│        Исходящий звонок                 │
│                                         │
│            02:45                        │
│                                         │
│ Ваш микрофон: [████████░░] 80%          │
│ Собеседник:   [██████░░░░] 60%          │
│                                         │
│ [🎤] [🔊] [⏸️] [📞]                     │
│                                         │
│ 🟢 HD Audio  🔵 Encrypted               │
└─────────────────────────────────────────┘
```

### 🎛️ Элементы управления:
- **🎤 Микрофон** - включить/выключить звук
- **🔊 Динамики** - управление звуком собеседника  
- **⏸️ Удержание** - поставить звонок на паузу
- **📞 Завершить** - закончить звонок

### 📊 Визуализация звука:
- **Анимированный аватар** - увеличивается при звуке
- **Полосы уровня** - показывают активность микрофона
- **Цветовая индикация** - зеленый/красный для статусов
- **Индикаторы качества** - HD Audio, Encrypted

## 🚀 Как использовать

### 1. Начало звонка:
1. Откройте страницу **"📞 Звонки"**
2. Введите номер или выберите контакт
3. Нажмите **"📞 Позвонить"**
4. Разрешите доступ к микрофону/камере
5. Дождитесь соединения

### 2. Во время звонка:

#### Голосовой звонок:
- Видите **аудиоплеер** с аватаром
- Управляйте звуком кнопками
- Следите за уровнями звука
- При необходимости включите видео

#### Видеозвонок:
- Видите **видеоплеер** с камерами
- Переключайте режимы отображения
- Управляйте видео и звуком
- Используйте полноэкранный режим

### 3. Управление во время звонка:

#### Включение видео:
1. Нажмите кнопку **📹 Видео**
2. Разрешите доступ к камере
3. Интерфейс переключится на видеоплеер
4. Выберите удобный режим отображения

#### Полноэкранный режим:
1. Нажмите кнопку **⛶ Полноэкранный**
2. Видео развернется на весь экран
3. Элементы управления скроются через 3 секунды
4. Пошевелите мышью для их показа

#### Picture-in-Picture:
1. Нажмите кнопку **📺 PiP**
2. Видео собеседника откроется в отдельном окне
3. Можете работать в других приложениях
4. Управление остается доступным

## ⚙️ Настройки качества

### В настройках WebRTC:

#### Видео:
- **Разрешение**: 480p, 720p, 1080p
- **Частота кадров**: 15, 24, 30, 60 FPS
- **Камера**: выбор устройства

#### Аудио:
- **Микрофон**: выбор устройства
- **Динамики**: выбор устройства
- **Фильтры**: эхо, шум, усиление

#### Сеть:
- **Пропускная способность**: ограничение трафика
- **Кодек**: Opus, G.711, G.722

## 🔧 Горячие клавиши

### Во время звонка:
- **Space** - включить/выключить микрофон
- **V** - включить/выключить видео
- **F** - полноэкранный режим
- **P** - Picture-in-Picture
- **H** - завершить звонок
- **S** - включить/выключить динамики

### В полноэкранном режиме:
- **Esc** - выйти из полноэкранного режима
- **Движение мыши** - показать элементы управления

## 📱 Мобильная версия

### Особенности:
- **Сенсорное управление** всех кнопок
- **Автоповорот** экрана для видео
- **Оптимизированные размеры** элементов
- **Жесты** для управления

### Рекомендации:
- Используйте **наушники** для лучшего качества
- **Хорошее освещение** для видеозвонков
- **Стабильный Wi-Fi** для HD качества
- **Вертикальная ориентация** для интерфейса

## 🔒 Безопасность

### Шифрование:
- **DTLS** для медиа-трафика
- **SRTP** для аудио/видео потоков
- **WSS** для сигнального канала

### Приватность:
- **Локальная обработка** видео
- **Прямое соединение** P2P
- **Контроль доступа** к устройствам
- **Индикаторы активности** камеры/микрофона

## 🆘 Устранение проблем

### Нет видео:
1. ✅ Проверьте разрешения камеры в браузере
2. ✅ Убедитесь что камера не используется другим приложением
3. ✅ Перезагрузите страницу
4. ✅ Проверьте настройки в WebRTC

### Нет звука:
1. ✅ Проверьте разрешения микрофона
2. ✅ Проверьте выбранные устройства
3. ✅ Убедитесь что микрофон не выключен
4. ✅ Проверьте громкость системы

### Плохое качество:
1. ✅ Проверьте скорость интернета
2. ✅ Уменьшите разрешение видео
3. ✅ Закройте другие приложения
4. ✅ Используйте проводное соединение

### Не работает полноэкранный режим:
1. ✅ Убедитесь что сайт открыт по HTTPS
2. ✅ Проверьте настройки браузера
3. ✅ Попробуйте другой браузер
4. ✅ Обновите браузер до последней версии

## 🎯 Советы для лучшего опыта

### Подготовка к звонку:
- 🎧 **Используйте наушники** - избегайте эха
- 💡 **Хорошее освещение** - лицо должно быть освещено
- 🔇 **Тихое место** - минимум фонового шума
- 🌐 **Стабильный интернет** - минимум 1 Мбит/с

### Во время звонка:
- 👀 **Смотрите в камеру** - для зрительного контакта
- 🎤 **Говорите четко** - не слишком близко к микрофону
- 📱 **Держите устройство устойчиво** - избегайте тряски
- 🔋 **Следите за батареей** - подключите зарядку

### Этикет видеозвонков:
- ✅ **Включайте видео** - для лучшего общения
- ✅ **Выключайте микрофон** - когда не говорите
- ✅ **Подготовьте фон** - уберите отвлекающие элементы
- ✅ **Тестируйте заранее** - проверьте камеру и микрофон

---

**🎉 Готово!** Теперь у вас есть профессиональная система видеозвонков с полным набором функций для качественного общения!
