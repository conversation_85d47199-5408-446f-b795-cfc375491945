import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from '@/store/authStore'
import { LoginForm } from '@/components/LoginForm'
import { Layout } from '@/components/Layout/Layout'
import { Dashboard } from '@/pages/Dashboard'
import { Clients } from '@/pages/Clients'
import { SipAccounts } from '@/pages/SipAccounts'
import { Tickets } from '@/pages/Tickets'
import { Settings } from '@/pages/Settings'

function App() {
  const { user, isLoading, isAuthenticated, checkAuth, setUser } = useAuthStore()

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const handleLoginSuccess = (userData: any) => {
    setUser(userData)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return <LoginForm onSuccess={handleLoginSuccess} />
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="clients" element={<Clients />} />
          <Route path="users" element={<div>Пользователи (в разработке)</div>} />
          <Route path="sip-accounts" element={<SipAccounts />} />
          <Route path="did-numbers" element={<div>DID Номера (в разработке)</div>} />
          <Route path="tickets" element={<Tickets />} />
          <Route path="billing" element={<div>Биллинг (в разработке)</div>} />
          <Route path="reports" element={<div>Отчеты (в разработке)</div>} />
          <Route path="logs" element={<div>Логи (в разработке)</div>} />
          <Route path="settings" element={<Settings />} />
          <Route path="tenants" element={<div>Арендаторы (в разработке)</div>} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Route>
      </Routes>
    </Router>
  )
}

export default App
