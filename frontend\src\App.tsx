import React, { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { LoginForm } from '@/components/LoginForm'
import { User } from '@supabase/supabase-js'

interface AppUser {
  id: string
  email: string
  role: string
  tenant_id: string
  first_name: string | null
  last_name: string | null
  tenant: {
    tenant_id: string
    name: string
    status: string
  } | null
}

function App() {
  const [user, setUser] = useState<AppUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Проверяем текущую сессию
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        // Получаем дополнительную информацию о пользователе
        fetchUserProfile(session.user)
      } else {
        setLoading(false)
      }
    })

    // Слушаем изменения аутентификации
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        await fetchUserProfile(session.user)
      } else if (event === 'SIGNED_OUT') {
        setUser(null)
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const fetchUserProfile = async (authUser: User) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          user_id,
          tenant_id,
          role,
          first_name,
          last_name,
          tenants (
            tenant_id,
            name,
            status
          )
        `)
        .eq('user_id', authUser.id)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        await supabase.auth.signOut()
        return
      }

      setUser({
        id: data.user_id,
        email: authUser.email || '',
        role: data.role,
        tenant_id: data.tenant_id,
        first_name: data.first_name,
        last_name: data.last_name,
        tenant: data.tenants
      })
    } catch (error) {
      console.error('Error:', error)
      await supabase.auth.signOut()
    } finally {
      setLoading(false)
    }
  }

  const handleLoginSuccess = (userData: AppUser) => {
    setUser(userData)
    setLoading(false)
  }

  const handleLogout = async () => {
    await supabase.auth.signOut()
    setUser(null)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!user) {
    return <LoginForm onSuccess={handleLoginSuccess} />
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                SPaaS Platform
              </h1>
              {user.tenant && (
                <span className="ml-4 px-3 py-1 bg-primary-100 text-primary-800 text-sm rounded-full">
                  {user.tenant.name}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-700">
                <span className="font-medium">
                  {user.first_name} {user.last_name}
                </span>
                <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                  {user.role}
                </span>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Выйти
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Добро пожаловать в SPaaS Platform!
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Многоарендаторная платформа для управления SIP-сервисами
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Клиенты
                  </h3>
                  <p className="text-gray-600">
                    Управление клиентами и их аккаунтами
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    SIP Аккаунты
                  </h3>
                  <p className="text-gray-600">
                    Настройка и мониторинг SIP аккаунтов
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Биллинг
                  </h3>
                  <p className="text-gray-600">
                    Управление счетами и платежами
                  </p>
                </div>
              </div>

              <div className="mt-8 text-sm text-gray-500">
                <p>Роль: <strong>{user.role}</strong></p>
                <p>Арендатор: <strong>{user.tenant?.name}</strong></p>
                <p>Email: <strong>{user.email}</strong></p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
