import React, { useEffect, useState } from 'react'
import { Plus, Search, Filter, Building, Users, UserCheck, UserX, Crown, AlertTriangle, Calendar, Mail, Phone, MapPin, Edit, Trash2, Pause, Play } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { demoTenants, demoStats } from '@/lib/demoData'

interface Tenant {
  tenant_id: string
  name: string
  domain: string
  status: string
  subscription_plan: string
  max_users: number
  max_clients: number
  current_users: number
  current_clients: number
  created_at: string
  billing_email: string
  contact_person: string
  phone: string
  address: string
  monthly_fee: number
  last_payment_date: string | null
  next_billing_date: string
}

export const Tenants: React.FC = () => {
  const { user } = useAuthStore()
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [planFilter, setPlanFilter] = useState<string>('all')

  useEffect(() => {
    fetchTenants()
  }, [user])

  const fetchTenants = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setTenants(demoTenants as any)
      } else {
        // Здесь будет реальный запрос к Supabase
        setTenants([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = 
      tenant.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.domain?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.contact_person?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter
    const matchesPlan = planFilter === 'all' || tenant.subscription_plan === planFilter

    return matchesSearch && matchesStatus && matchesPlan
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-4 w-4 text-green-600" />
      case 'trial':
        return <Calendar className="h-4 w-4 text-blue-600" />
      case 'suspended':
        return <Pause className="h-4 w-4 text-red-600" />
      case 'inactive':
        return <UserX className="h-4 w-4 text-gray-600" />
      default:
        return <Building className="h-4 w-4 text-gray-400" />
    }
  }

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'enterprise':
        return <Crown className="h-4 w-4 text-purple-600" />
      case 'professional':
        return <Building className="h-4 w-4 text-blue-600" />
      case 'basic':
        return <Users className="h-4 w-4 text-green-600" />
      default:
        return <Building className="h-4 w-4 text-gray-400" />
    }
  }

  const getPlanDisplayName = (plan: string) => {
    const plans: Record<string, string> = {
      'basic': 'Базовый',
      'professional': 'Профессиональный',
      'enterprise': 'Корпоративный'
    }
    return plans[plan] || plan
  }

  const getUsagePercentage = (current: number, max: number) => {
    return Math.round((current / max) * 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const handleToggleStatus = (tenantId: string) => {
    setTenants(prev => prev.map(t => 
      t.tenant_id === tenantId
        ? { ...t, status: t.status === 'active' ? 'suspended' : 'active' }
        : t
    ))
  }

  const handleDeleteTenant = (tenantId: string) => {
    if (confirm('Вы уверены, что хотите удалить этого арендатора? Это действие необратимо!')) {
      setTenants(prev => prev.filter(t => t.tenant_id !== tenantId))
    }
  }

  // Только Provider может управлять арендаторами
  if (user?.role !== 'provider') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Доступ ограничен</h2>
          <p className="text-gray-500">Только провайдеры могут управлять арендаторами</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Арендаторы</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление арендаторами платформы
          </p>
        </div>
        <button className="btn btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Добавить арендатора
        </button>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon blue">
            <Building className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего арендаторов</h3>
            <p>{demoStats.totalTenants}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <UserCheck className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Активные</h3>
            <p>{demoStats.activeTenants}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon blue">
            <Calendar className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Пробные</h3>
            <p>{demoStats.trialTenants}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon red">
            <Pause className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Приостановленные</h3>
            <p>{demoStats.suspendedTenants}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <Crown className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Месячная выручка</h3>
            <p>{formatCurrency(tenants.reduce((sum, t) => sum + t.monthly_fee, 0))}</p>
          </div>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="card">
        <div className="card-header">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Поиск арендаторов..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все статусы</option>
                <option value="active">Активные</option>
                <option value="trial">Пробные</option>
                <option value="suspended">Приостановленные</option>
                <option value="inactive">Неактивные</option>
              </select>
            </div>

            {/* Plan Filter */}
            <div className="sm:w-48">
              <select
                value={planFilter}
                onChange={(e) => setPlanFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все планы</option>
                <option value="basic">Базовый</option>
                <option value="professional">Профессиональный</option>
                <option value="enterprise">Корпоративный</option>
              </select>
            </div>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>Арендатор</th>
                <th>План</th>
                <th>Статус</th>
                <th>Использование</th>
                <th>Контакты</th>
                <th>Биллинг</th>
                <th>Действия</th>
              </tr>
            </thead>
            <tbody>
              {filteredTenants.map((tenant) => (
                <tr key={tenant.tenant_id}>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">
                        {tenant.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {tenant.domain}
                      </div>
                      <div className="text-xs text-gray-400">
                        Создан: {formatDate(tenant.created_at)}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getPlanIcon(tenant.subscription_plan)}
                      <span className="ml-2 text-sm">
                        {getPlanDisplayName(tenant.subscription_plan)}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getStatusIcon(tenant.status)}
                      <span className={`ml-2 badge ${tenant.status}`}>
                        {tenant.status}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Пользователи:</span>
                        <span className={getUsageColor(getUsagePercentage(tenant.current_users, tenant.max_users))}>
                          {tenant.current_users}/{tenant.max_users}
                        </span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Клиенты:</span>
                        <span className={getUsageColor(getUsagePercentage(tenant.current_clients, tenant.max_clients))}>
                          {tenant.current_clients}/{tenant.max_clients}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="space-y-1">
                      <div className="flex items-center text-xs">
                        <Users className="h-3 w-3 mr-1" />
                        {tenant.contact_person}
                      </div>
                      <div className="flex items-center text-xs">
                        <Mail className="h-3 w-3 mr-1" />
                        {tenant.billing_email}
                      </div>
                      <div className="flex items-center text-xs">
                        <Phone className="h-3 w-3 mr-1" />
                        {tenant.phone}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {formatCurrency(tenant.monthly_fee)}/мес
                      </div>
                      <div className="text-xs text-gray-500">
                        Следующий платеж: {formatDate(tenant.next_billing_date)}
                      </div>
                      {tenant.last_payment_date && (
                        <div className="text-xs text-gray-400">
                          Последний: {formatDate(tenant.last_payment_date)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleToggleStatus(tenant.tenant_id)}
                        className={`p-1 text-gray-400 hover:${tenant.status === 'active' ? 'text-red-600' : 'text-green-600'}`}
                        title={tenant.status === 'active' ? 'Приостановить' : 'Активировать'}
                      >
                        {tenant.status === 'active' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </button>
                      
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Редактировать"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => handleDeleteTenant(tenant.tenant_id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Удалить"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredTenants.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm || statusFilter !== 'all' || planFilter !== 'all'
                  ? 'Арендаторы не найдены по заданным критериям'
                  : 'Пока нет арендаторов'
                }
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
