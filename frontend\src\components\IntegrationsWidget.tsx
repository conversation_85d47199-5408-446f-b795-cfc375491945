import React from 'react'
import { <PERSON>ap, Check, X, Alert<PERSON>ircle, ArrowR<PERSON> } from 'lucide-react'
import { Link } from 'react-router-dom'

interface Integration {
  id: string
  name: string
  display_name: string
  status: 'connected' | 'disconnected' | 'error' | 'pending'
  icon: string
  color: string
}

interface IntegrationsWidgetProps {
  integrations: Integration[]
}

export const IntegrationsWidget: React.FC<IntegrationsWidgetProps> = ({ integrations }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Check className="h-3 w-3 text-green-600" />
      case 'disconnected':
        return <X className="h-3 w-3 text-gray-400" />
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-600" />
      case 'pending':
        return <AlertCircle className="h-3 w-3 text-yellow-600" />
      default:
        return <X className="h-3 w-3 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-600'
      case 'disconnected':
        return 'text-gray-400'
      case 'error':
        return 'text-red-600'
      case 'pending':
        return 'text-yellow-600'
      default:
        return 'text-gray-400'
    }
  }

  const connectedCount = integrations.filter(i => i.status === 'connected').length
  const errorCount = integrations.filter(i => i.status === 'error').length

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Zap className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Интеграции</h3>
          </div>
          <Link 
            to="/integrations"
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            Все интеграции
            <ArrowRight className="h-3 w-3 ml-1" />
          </Link>
        </div>
      </div>

      <div className="p-6">
        {/* Статистика */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{connectedCount}</div>
            <div className="text-xs text-gray-500">Подключено</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{errorCount}</div>
            <div className="text-xs text-gray-500">Ошибки</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">{integrations.length}</div>
            <div className="text-xs text-gray-500">Всего</div>
          </div>
        </div>

        {/* Список интеграций */}
        <div className="space-y-3">
          {integrations.slice(0, 6).map((integration) => (
            <div key={integration.id} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-6 h-6 ${integration.color} rounded text-white text-xs flex items-center justify-center mr-3`}>
                  {integration.icon}
                </div>
                <span className="text-sm text-gray-900">{integration.display_name}</span>
              </div>
              <div className="flex items-center">
                {getStatusIcon(integration.status)}
                <span className={`text-xs ml-1 ${getStatusColor(integration.status)}`}>
                  {integration.status === 'connected' && 'Подключено'}
                  {integration.status === 'disconnected' && 'Отключено'}
                  {integration.status === 'error' && 'Ошибка'}
                  {integration.status === 'pending' && 'Ожидание'}
                </span>
              </div>
            </div>
          ))}

          {integrations.length === 0 && (
            <div className="text-center py-4">
              <div className="text-gray-500 text-sm">
                Интеграции не настроены
              </div>
            </div>
          )}

          {integrations.length > 6 && (
            <div className="text-center pt-2">
              <Link 
                to="/integrations"
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Показать еще {integrations.length - 6}
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
