{"hash": "d23569ab", "browserHash": "1aeb8efb", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "78b7f83a", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "23e516b5", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9682a768", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "69aa6ef8", "needsInterop": true}, "@headlessui/react": {"src": "../../../../node_modules/@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "799bc578", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../../../node_modules/@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "1c800e3b", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "39680391", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "47233c62", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "4572dbaf", "needsInterop": true}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "5904dcea", "needsInterop": false}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "9ade0bb9", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "73eddd01", "needsInterop": false}, "zustand": {"src": "../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "2a30cef6", "needsInterop": false}, "zustand/middleware": {"src": "../../../../node_modules/zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "d56cfad4", "needsInterop": false}}, "chunks": {"browser-OKUMD2QK": {"file": "browser-OKUMD2QK.js"}, "chunk-T36AEHKX": {"file": "chunk-T36AEHKX.js"}, "chunk-BXQFRZHP": {"file": "chunk-BXQFRZHP.js"}, "chunk-HWDFEQAI": {"file": "chunk-HWDFEQAI.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}