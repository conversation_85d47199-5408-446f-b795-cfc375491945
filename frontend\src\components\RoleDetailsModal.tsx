import React from 'react'
import { Shield, Users, Calendar, Check, X, Lock } from 'lucide-react'
import { <PERSON><PERSON>, ModalFooter } from '@/components/Modal'
import { formatDate } from '@/lib/utils'

interface Permission {
  id: string
  name: string
  display_name: string
  category: string
  description: string
}

interface Role {
  role_id: string
  name: string
  display_name: string
  description: string
  permissions: string[]
  is_system: boolean
  created_at: string
  updated_at: string
  users_count: number
}

interface RoleDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  role: Role | null
  permissions: Permission[]
}

export const RoleDetailsModal: React.FC<RoleDetailsModalProps> = ({
  isOpen,
  onClose,
  role,
  permissions
}) => {
  if (!role) return null

  const rolePermissions = permissions.filter(p => role.permissions.includes(p.id))
  const hasAllPermissions = role.permissions.includes('*')

  const groupedPermissions = rolePermissions.reduce((groups, permission) => {
    const category = permission.category
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(permission)
    return groups
  }, {} as Record<string, Permission[]>)

  const getRoleIcon = (roleName: string) => {
    switch (roleName) {
      case 'provider':
        return <Shield className="h-8 w-8 text-purple-600" />
      case 'reseller':
        return <Users className="h-8 w-8 text-blue-600" />
      case 'admin':
        return <Lock className="h-8 w-8 text-green-600" />
      case 'support':
        return <Users className="h-8 w-8 text-orange-600" />
      default:
        return <Shield className="h-8 w-8 text-gray-600" />
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Детали роли"
      size="lg"
    >
      <div className="space-y-6">
        {/* Основная информация */}
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            {getRoleIcon(role.name)}
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <h2 className="text-xl font-bold text-gray-900">
                {role.display_name}
              </h2>
              <span className={`badge ${role.is_system ? 'badge-warning' : 'badge-success'}`}>
                {role.is_system ? 'Системная' : 'Пользовательская'}
              </span>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              {role.name}
            </p>
            {role.description && (
              <p className="text-gray-700 mt-2">
                {role.description}
              </p>
            )}
          </div>
        </div>

        {/* Статистика */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Users className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <div className="text-lg font-semibold text-blue-900">
                  {role.users_count}
                </div>
                <div className="text-sm text-blue-600">
                  Пользователей
                </div>
              </div>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Check className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <div className="text-lg font-semibold text-green-900">
                  {hasAllPermissions ? 'Все' : role.permissions.length}
                </div>
                <div className="text-sm text-green-600">
                  Разрешений
                </div>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-purple-600 mr-2" />
              <div>
                <div className="text-lg font-semibold text-purple-900">
                  {formatDate(role.created_at, 'short')}
                </div>
                <div className="text-sm text-purple-600">
                  Создана
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Разрешения */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Разрешения
          </h3>

          {hasAllPermissions ? (
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-purple-600 mr-2" />
                <div>
                  <div className="font-medium text-purple-900">
                    Полный доступ
                  </div>
                  <div className="text-sm text-purple-700">
                    Эта роль имеет доступ ко всем функциям системы
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="max-h-64 overflow-y-auto border rounded-lg">
              {Object.keys(groupedPermissions).length > 0 ? (
                Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                  <div key={category} className="border-b border-gray-200 last:border-b-0">
                    <div className="bg-gray-50 px-4 py-3">
                      <h4 className="font-medium text-gray-900 flex items-center">
                        <Check className="h-4 w-4 text-green-600 mr-2" />
                        {category} ({categoryPermissions.length})
                      </h4>
                    </div>
                    
                    <div className="p-4 space-y-2">
                      {categoryPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-start">
                          <Check className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">
                              {permission.display_name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {permission.description}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <X className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <div className="text-gray-500">
                    У этой роли нет разрешений
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Дополнительная информация */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">
            Дополнительная информация
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">ID роли:</span>
              <div className="font-mono text-gray-900">{role.role_id}</div>
            </div>
            <div>
              <span className="text-gray-500">Системное имя:</span>
              <div className="font-mono text-gray-900">{role.name}</div>
            </div>
            <div>
              <span className="text-gray-500">Создана:</span>
              <div className="text-gray-900">{formatDate(role.created_at)}</div>
            </div>
            <div>
              <span className="text-gray-500">Обновлена:</span>
              <div className="text-gray-900">{formatDate(role.updated_at)}</div>
            </div>
          </div>
        </div>

        {/* Предупреждения для системных ролей */}
        {role.is_system && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <Lock className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <div className="font-medium text-yellow-900">
                  Системная роль
                </div>
                <div className="text-sm text-yellow-700 mt-1">
                  Эта роль является системной и не может быть изменена или удалена. 
                  Разрешения системных ролей определяются логикой приложения.
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <ModalFooter>
        <button
          type="button"
          onClick={onClose}
          className="btn btn-secondary"
        >
          Закрыть
        </button>
      </ModalFooter>
    </Modal>
  )
}
