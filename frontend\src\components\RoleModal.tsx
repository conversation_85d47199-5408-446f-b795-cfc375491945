import React, { useState, useEffect } from 'react'
import { Shield, X, Check, Search } from 'lucide-react'
import { <PERSON><PERSON>, ModalFooter } from '@/components/Modal'

interface Permission {
  id: string
  name: string
  display_name: string
  category: string
  description: string
}

interface Role {
  role_id?: string
  name: string
  display_name: string
  description: string
  permissions: string[]
  is_system?: boolean
}

interface RoleModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (role: Role) => void
  role?: Role | null
  permissions: Permission[]
}

export const RoleModal: React.FC<RoleModalProps> = ({
  isOpen,
  onClose,
  onSave,
  role,
  permissions
}) => {
  const [formData, setFormData] = useState<Role>({
    name: '',
    display_name: '',
    description: '',
    permissions: []
  })
  const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  useEffect(() => {
    if (role) {
      setFormData({
        role_id: role.role_id,
        name: role.name,
        display_name: role.display_name,
        description: role.description,
        permissions: role.permissions,
        is_system: role.is_system
      })
      setSelectedPermissions(new Set(role.permissions))
    } else {
      setFormData({
        name: '',
        display_name: '',
        description: '',
        permissions: []
      })
      setSelectedPermissions(new Set())
    }
  }, [role, isOpen])

  const categories = Array.from(new Set(permissions.map(p => p.category)))

  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = 
      permission.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = selectedCategory === 'all' || permission.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const groupedPermissions = filteredPermissions.reduce((groups, permission) => {
    const category = permission.category
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(permission)
    return groups
  }, {} as Record<string, Permission[]>)

  const handlePermissionToggle = (permissionId: string) => {
    const newSelected = new Set(selectedPermissions)
    if (newSelected.has(permissionId)) {
      newSelected.delete(permissionId)
    } else {
      newSelected.add(permissionId)
    }
    setSelectedPermissions(newSelected)
    setFormData(prev => ({
      ...prev,
      permissions: Array.from(newSelected)
    }))
  }

  const handleSelectAllInCategory = (category: string) => {
    const categoryPermissions = permissions.filter(p => p.category === category)
    const allSelected = categoryPermissions.every(p => selectedPermissions.has(p.id))
    
    const newSelected = new Set(selectedPermissions)
    
    if (allSelected) {
      // Убираем все разрешения категории
      categoryPermissions.forEach(p => newSelected.delete(p.id))
    } else {
      // Добавляем все разрешения категории
      categoryPermissions.forEach(p => newSelected.add(p.id))
    }
    
    setSelectedPermissions(newSelected)
    setFormData(prev => ({
      ...prev,
      permissions: Array.from(newSelected)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.display_name) {
      alert('Заполните обязательные поля')
      return
    }

    onSave(formData)
    handleClose()
  }

  const handleClose = () => {
    setFormData({
      name: '',
      display_name: '',
      description: '',
      permissions: []
    })
    setSelectedPermissions(new Set())
    setSearchTerm('')
    setSelectedCategory('all')
    onClose()
  }

  const isEditing = !!role?.role_id

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? 'Редактирование роли' : 'Создание роли'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Основная информация */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="form-group">
            <label className="form-label required">
              Системное имя
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="form-input"
              placeholder="manager"
              disabled={isEditing && formData.is_system}
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Используется в системе (только латинские буквы и подчеркивания)
            </p>
          </div>

          <div className="form-group">
            <label className="form-label required">
              Отображаемое имя
            </label>
            <input
              type="text"
              value={formData.display_name}
              onChange={(e) => setFormData(prev => ({ ...prev, display_name: e.target.value }))}
              className="form-input"
              placeholder="Менеджер"
              required
            />
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">
            Описание
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="form-input"
            rows={3}
            placeholder="Описание роли и её назначения..."
          />
        </div>

        {/* Разрешения */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Разрешения ({selectedPermissions.size})
            </h3>
            <div className="text-sm text-gray-500">
              Выберите разрешения для этой роли
            </div>
          </div>

          {/* Фильтры разрешений */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Поиск разрешений..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            <div className="sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="form-input"
              >
                <option value="all">Все категории</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Список разрешений по категориям */}
          <div className="max-h-96 overflow-y-auto border rounded-lg">
            {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
              <div key={category} className="border-b border-gray-200 last:border-b-0">
                <div className="bg-gray-50 px-4 py-3 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{category}</h4>
                  <button
                    type="button"
                    onClick={() => handleSelectAllInCategory(category)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {categoryPermissions.every(p => selectedPermissions.has(p.id))
                      ? 'Снять все'
                      : 'Выбрать все'
                    }
                  </button>
                </div>
                
                <div className="p-4 space-y-3">
                  {categoryPermissions.map((permission) => (
                    <div key={permission.id} className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          checked={selectedPermissions.has(permission.id)}
                          onChange={() => handlePermissionToggle(permission.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="flex items-center">
                          <label className="text-sm font-medium text-gray-900">
                            {permission.display_name}
                          </label>
                          {selectedPermissions.has(permission.id) && (
                            <Check className="h-4 w-4 text-green-600 ml-2" />
                          )}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {permission.name}
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          {permission.description}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {Object.keys(groupedPermissions).length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-500">
                  Разрешения не найдены по заданным критериям
                </div>
              </div>
            )}
          </div>
        </div>
      </form>

      <ModalFooter>
        <button
          type="button"
          onClick={handleClose}
          className="btn btn-secondary"
        >
          Отмена
        </button>
        <button
          onClick={handleSubmit}
          className="btn btn-primary"
        >
          <Shield className="h-4 w-4 mr-2" />
          {isEditing ? 'Сохранить' : 'Создать роль'}
        </button>
      </ModalFooter>
    </Modal>
  )
}
