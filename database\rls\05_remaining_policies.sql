-- RLS политики для остальных таблиц

-- =============================================
-- SIP ACCOUNTS POLICIES
-- =============================================

-- Provider может видеть и управлять всеми SIP аккаунтами
CREATE POLICY "Provider full access to sip_accounts" ON sip_accounts
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи арендатора могут видеть SIP аккаунты своего арендатора
CREATE POLICY "Tenant users can view sip_accounts" ON sip_accounts
  FOR SELECT 
  USING (auth.has_tenant_access(tenant_id));

-- Администраторы арендатора могут управлять SIP аккаунтами
CREATE POLICY "Tenant admins can manage sip_accounts" ON sip_accounts
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  );

-- Support может видеть и обновлять SIP аккаунты
CREATE POLICY "Support can manage sip_accounts" ON sip_accounts
  FOR ALL 
  USING (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  );

-- =============================================
-- DID NUMBERS POLICIES
-- =============================================

-- Provider может видеть и управлять всеми DID номерами
CREATE POLICY "Provider full access to did_numbers" ON did_numbers
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи арендатора могут видеть DID номера своего арендатора
CREATE POLICY "Tenant users can view did_numbers" ON did_numbers
  FOR SELECT 
  USING (auth.has_tenant_access(tenant_id));

-- Администраторы арендатора могут управлять DID номерами
CREATE POLICY "Tenant admins can manage did_numbers" ON did_numbers
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  );

-- Support может видеть и обновлять DID номера
CREATE POLICY "Support can manage did_numbers" ON did_numbers
  FOR ALL 
  USING (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  );

-- =============================================
-- TICKETS POLICIES
-- =============================================

-- Provider может видеть и управлять всеми тикетами
CREATE POLICY "Provider full access to tickets" ON tickets
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи арендатора могут видеть тикеты своего арендатора
CREATE POLICY "Tenant users can view tickets" ON tickets
  FOR SELECT 
  USING (auth.has_tenant_access(tenant_id));

-- Администраторы и Support могут управлять тикетами
CREATE POLICY "Admins and support can manage tickets" ON tickets
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.user_role() IN ('reseller', 'admin', 'support')
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.user_role() IN ('reseller', 'admin', 'support')
    AND tenant_id = auth.user_tenant_id()
  );

-- Staff может видеть и создавать тикеты
CREATE POLICY "Staff can view and create tickets" ON tickets
  FOR SELECT 
  USING (
    auth.user_role() = 'staff'
    AND tenant_id = auth.user_tenant_id()
  );

CREATE POLICY "Staff can create tickets" ON tickets
  FOR INSERT 
  WITH CHECK (
    auth.user_role() = 'staff'
    AND tenant_id = auth.user_tenant_id()
  );

-- Клиенты могут видеть только свои тикеты
CREATE POLICY "Clients can view own tickets" ON tickets
  FOR SELECT 
  USING (
    auth.user_role() = 'client'
    AND EXISTS (
      SELECT 1 FROM users u 
      WHERE u.user_id = auth.user_id() 
      AND u.tenant_id = tickets.tenant_id
    )
  );

-- =============================================
-- TICKET COMMENTS POLICIES
-- =============================================

-- Provider может видеть и управлять всеми комментариями
CREATE POLICY "Provider full access to ticket_comments" ON ticket_comments
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи могут видеть комментарии к тикетам, к которым имеют доступ
CREATE POLICY "Users can view accessible ticket_comments" ON ticket_comments
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM tickets t 
      WHERE t.ticket_id = ticket_comments.ticket_id 
      AND auth.has_tenant_access(t.tenant_id)
    )
  );

-- Пользователи могут создавать комментарии к доступным тикетам
CREATE POLICY "Users can create ticket_comments" ON ticket_comments
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM tickets t 
      WHERE t.ticket_id = ticket_comments.ticket_id 
      AND auth.has_tenant_access(t.tenant_id)
    )
    AND author_id = auth.user_id()
  );

-- =============================================
-- INVOICES POLICIES
-- =============================================

-- Provider может видеть и управлять всеми инвойсами
CREATE POLICY "Provider full access to invoices" ON invoices
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи арендатора могут видеть инвойсы своего арендатора
CREATE POLICY "Tenant users can view invoices" ON invoices
  FOR SELECT 
  USING (auth.has_tenant_access(tenant_id));

-- Администраторы арендатора могут управлять инвойсами
CREATE POLICY "Tenant admins can manage invoices" ON invoices
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  );

-- Клиенты могут видеть только свои инвойсы
CREATE POLICY "Clients can view own invoices" ON invoices
  FOR SELECT 
  USING (
    auth.user_role() = 'client'
    AND EXISTS (
      SELECT 1 FROM users u 
      WHERE u.user_id = auth.user_id() 
      AND u.tenant_id = invoices.tenant_id
    )
  );

-- =============================================
-- INVOICE ITEMS POLICIES
-- =============================================

-- Provider может видеть и управлять всеми позициями инвойсов
CREATE POLICY "Provider full access to invoice_items" ON invoice_items
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи могут видеть позиции инвойсов, к которым имеют доступ
CREATE POLICY "Users can view accessible invoice_items" ON invoice_items
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM invoices i 
      WHERE i.invoice_id = invoice_items.invoice_id 
      AND auth.has_tenant_access(i.tenant_id)
    )
  );

-- Администраторы могут управлять позициями инвойсов
CREATE POLICY "Admins can manage invoice_items" ON invoice_items
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM invoices i 
      WHERE i.invoice_id = invoice_items.invoice_id 
      AND auth.has_tenant_access(i.tenant_id)
      AND (auth.is_provider() OR auth.is_tenant_admin())
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM invoices i 
      WHERE i.invoice_id = invoice_items.invoice_id 
      AND auth.has_tenant_access(i.tenant_id)
      AND (auth.is_provider() OR auth.is_tenant_admin())
    )
  );

-- =============================================
-- PAYMENTS POLICIES
-- =============================================

-- Provider может видеть и управлять всеми платежами
CREATE POLICY "Provider full access to payments" ON payments
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи арендатора могут видеть платежи своего арендатора
CREATE POLICY "Tenant users can view payments" ON payments
  FOR SELECT 
  USING (auth.has_tenant_access(tenant_id));

-- Администраторы арендатора могут управлять платежами
CREATE POLICY "Tenant admins can manage payments" ON payments
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  );

-- =============================================
-- LOGS POLICIES
-- =============================================

-- Provider может видеть все логи
CREATE POLICY "Provider can view all logs" ON logs
  FOR SELECT 
  USING (auth.is_provider());

-- Пользователи арендатора могут видеть логи своего арендатора
CREATE POLICY "Tenant users can view tenant logs" ON logs
  FOR SELECT 
  USING (
    NOT auth.is_provider()
    AND auth.has_tenant_access(tenant_id)
  );

-- Все аутентифицированные пользователи могут создавать логи
CREATE POLICY "Authenticated users can create logs" ON logs
  FOR INSERT 
  WITH CHECK (auth.user_id() IS NOT NULL);

-- Запрет на обновление и удаление логов (только чтение и создание)
CREATE POLICY "No updates to logs" ON logs
  FOR UPDATE 
  USING (false);

CREATE POLICY "No deletes from logs" ON logs
  FOR DELETE 
  USING (false);

-- Комментарии к политикам
COMMENT ON POLICY "Provider full access to sip_accounts" ON sip_accounts IS 
  'Provider имеет полный доступ ко всем SIP аккаунтам';

COMMENT ON POLICY "Provider full access to did_numbers" ON did_numbers IS 
  'Provider имеет полный доступ ко всем DID номерам';

COMMENT ON POLICY "Provider full access to tickets" ON tickets IS 
  'Provider имеет полный доступ ко всем тикетам';

COMMENT ON POLICY "Clients can view own tickets" ON tickets IS 
  'Клиенты могут видеть только свои тикеты';

COMMENT ON POLICY "No updates to logs" ON logs IS 
  'Логи нельзя изменять - только создавать и читать';

COMMENT ON POLICY "No deletes from logs" ON logs IS 
  'Логи нельзя удалять - только создавать и читать';
