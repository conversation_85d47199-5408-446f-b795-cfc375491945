import React, { useState } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, AreaChart, Area } from 'recharts'
import { TrendingUp, TrendingDown, Users, Phone, DollarSign, Calendar, Download, Filter, Eye, BarChart3 } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatCurrency } from '@/lib/utils'

// Демо данные для графиков
const revenueData = [
  { month: 'Янв', revenue: 85000, clients: 12, calls: 2450 },
  { month: 'Фев', revenue: 92000, clients: 15, calls: 2890 },
  { month: 'Мар', revenue: 78000, clients: 11, calls: 2234 },
  { month: 'Апр', revenue: 105000, clients: 18, calls: 3456 },
  { month: 'Май', revenue: 118000, clients: 22, calls: 3890 },
  { month: 'Июн', revenue: 125000, clients: 25, calls: 4123 },
  { month: 'Июл', revenue: 132000, clients: 28, calls: 4567 },
  { month: 'Авг', revenue: 128000, clients: 26, calls: 4234 },
  { month: 'Сен', revenue: 145000, clients: 32, calls: 4890 },
  { month: 'Окт', revenue: 138000, clients: 30, calls: 4567 },
  { month: 'Ноя', revenue: 152000, clients: 35, calls: 5123 },
  { month: 'Дек', revenue: 165000, clients: 38, calls: 5456 }
]

const callsData = [
  { day: 'Пн', incoming: 245, outgoing: 189, duration: 1234 },
  { day: 'Вт', incoming: 312, outgoing: 234, duration: 1567 },
  { day: 'Ср', incoming: 289, outgoing: 198, duration: 1345 },
  { day: 'Чт', incoming: 356, outgoing: 267, duration: 1789 },
  { day: 'Пт', incoming: 398, outgoing: 301, duration: 1923 },
  { day: 'Сб', incoming: 178, outgoing: 134, duration: 987 },
  { day: 'Вс', incoming: 156, outgoing: 98, duration: 756 }
]

const serviceDistribution = [
  { name: 'SIP Аккаунты', value: 45, color: '#3b82f6', revenue: 67500 },
  { name: 'DID Номера', value: 30, color: '#10b981', revenue: 49500 },
  { name: 'Исходящие звонки', value: 20, color: '#f59e0b', revenue: 33000 },
  { name: 'Дополнительные услуги', value: 5, color: '#ef4444', revenue: 8250 }
]

const ticketStats = [
  { category: 'Техническая поддержка', open: 12, resolved: 45, avg_time: 4.2 },
  { category: 'Биллинг', open: 3, resolved: 28, avg_time: 2.1 },
  { category: 'Настройка', open: 8, resolved: 32, avg_time: 6.5 },
  { category: 'Другое', open: 2, resolved: 15, avg_time: 3.8 }
]

const clientGrowthData = [
  { month: 'Янв', new_clients: 3, churned_clients: 1, active_clients: 12 },
  { month: 'Фев', new_clients: 5, churned_clients: 2, active_clients: 15 },
  { month: 'Мар', new_clients: 2, churned_clients: 6, active_clients: 11 },
  { month: 'Апр', new_clients: 8, churned_clients: 1, active_clients: 18 },
  { month: 'Май', new_clients: 6, churned_clients: 2, active_clients: 22 },
  { month: 'Июн', new_clients: 4, churned_clients: 1, active_clients: 25 },
  { month: 'Июл', new_clients: 5, churned_clients: 2, active_clients: 28 },
  { month: 'Авг', new_clients: 3, churned_clients: 5, active_clients: 26 },
  { month: 'Сен', new_clients: 8, churned_clients: 2, active_clients: 32 },
  { month: 'Окт', new_clients: 4, churned_clients: 6, active_clients: 30 },
  { month: 'Ноя', new_clients: 7, churned_clients: 2, active_clients: 35 },
  { month: 'Дек', new_clients: 5, churned_clients: 2, active_clients: 38 }
]

const performanceData = [
  { hour: '00:00', calls: 45, quality: 98.2, errors: 2 },
  { hour: '04:00', calls: 23, quality: 99.1, errors: 1 },
  { hour: '08:00', calls: 156, quality: 97.8, errors: 5 },
  { hour: '12:00', calls: 234, quality: 96.5, errors: 8 },
  { hour: '16:00', calls: 189, quality: 97.2, errors: 6 },
  { hour: '20:00', calls: 98, quality: 98.7, errors: 3 }
]

export const Analytics: React.FC = () => {
  const { user } = useAuthStore()
  const [dateRange, setDateRange] = useState('last_month')
  const [viewType, setViewType] = useState('overview')

  const currentMonth = revenueData[revenueData.length - 1]
  const previousMonth = revenueData[revenueData.length - 2]
  const revenueGrowth = ((currentMonth.revenue - previousMonth.revenue) / previousMonth.revenue * 100).toFixed(1)
  const clientsGrowth = ((currentMonth.clients - previousMonth.clients) / previousMonth.clients * 100).toFixed(1)
  const callsGrowth = ((currentMonth.calls - previousMonth.calls) / previousMonth.calls * 100).toFixed(1)

  const totalCalls = callsData.reduce((sum, day) => sum + day.incoming + day.outgoing, 0)
  const avgCallDuration = callsData.reduce((sum, day) => sum + day.duration, 0) / callsData.length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Аналитика</h1>
          <p className="mt-1 text-sm text-gray-500">
            Визуальный анализ данных и трендов
          </p>
        </div>
        <div className="flex gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="form-input"
          >
            <option value="last_week">Последняя неделя</option>
            <option value="last_month">Последний месяц</option>
            <option value="last_quarter">Последний квартал</option>
            <option value="last_year">Последний год</option>
          </select>
          <select
            value={viewType}
            onChange={(e) => setViewType(e.target.value)}
            className="form-input"
          >
            <option value="overview">Обзор</option>
            <option value="financial">Финансы</option>
            <option value="operational">Операции</option>
            <option value="technical">Техническое</option>
          </select>
          <button className="btn btn-primary">
            <Download className="h-4 w-4 mr-2" />
            Экспорт
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon blue">
            <DollarSign className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Выручка за месяц</h3>
            <p>{formatCurrency(currentMonth.revenue)}</p>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+{revenueGrowth}%</span>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <Users className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Активные клиенты</h3>
            <p>{currentMonth.clients}</p>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+{clientsGrowth}%</span>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <Phone className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Звонков за месяц</h3>
            <p>{currentMonth.calls.toLocaleString()}</p>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+{callsGrowth}%</span>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <BarChart3 className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Средняя длительность</h3>
            <p>{Math.round(avgCallDuration / 60)} мин</p>
            <div className="flex items-center mt-1">
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600">-2.3%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Динамика выручки</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={revenueData}>
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `${value / 1000}k`} />
                <Tooltip 
                  formatter={(value: number) => [formatCurrency(value), 'Выручка']}
                  labelStyle={{ color: '#374151' }}
                />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#3b82f6" 
                  fillOpacity={1} 
                  fill="url(#colorRevenue)" 
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Client Growth */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Рост клиентской базы</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={clientGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="new_clients" 
                  stroke="#10b981" 
                  strokeWidth={3}
                  name="Новые клиенты"
                />
                <Line 
                  type="monotone" 
                  dataKey="churned_clients" 
                  stroke="#ef4444" 
                  strokeWidth={3}
                  name="Ушедшие клиенты"
                />
                <Line 
                  type="monotone" 
                  dataKey="active_clients" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  name="Активные клиенты"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Calls Statistics */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Статистика звонков</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={callsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="incoming" fill="#10b981" name="Входящие" />
                <Bar dataKey="outgoing" fill="#3b82f6" name="Исходящие" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Service Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Распределение услуг</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={serviceDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {serviceDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number, name: string, props: any) => [
                    `${value}% (${formatCurrency(props.payload.revenue)})`, 
                    'Доля'
                  ]} 
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Metrics */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Производительность системы</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Bar yAxisId="left" dataKey="calls" fill="#3b82f6" name="Звонки" />
                <Line yAxisId="right" type="monotone" dataKey="quality" stroke="#10b981" strokeWidth={2} name="Качество %" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Support Metrics */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Метрики поддержки</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={ticketStats} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="category" type="category" width={120} />
                <Tooltip />
                <Bar dataKey="open" fill="#f59e0b" name="Открытые" />
                <Bar dataKey="resolved" fill="#10b981" name="Решенные" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  )
}
