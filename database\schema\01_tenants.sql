-- Таблица арендаторов (реселлеров)
CREATE TABLE IF NOT EXISTS tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
    subscription_plan VARCHAR(100),
    max_users INTEGER DEFAULT 10,
    max_clients INTEGER DEFAULT 100,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Индексы
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_created_at ON tenants(created_at);

-- Триггер для обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at 
    BEFORE UPDATE ON tenants 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Комментарии
COMMENT ON TABLE tenants IS 'Арендаторы платформы (реселлеры)';
COMMENT ON COLUMN tenants.tenant_id IS 'Уникальный идентификатор арендатора';
COMMENT ON COLUMN tenants.name IS 'Название компании арендатора';
COMMENT ON COLUMN tenants.domain IS 'Домен арендатора (опционально)';
COMMENT ON COLUMN tenants.status IS 'Статус арендатора: active, suspended, inactive';
COMMENT ON COLUMN tenants.subscription_plan IS 'План подписки';
COMMENT ON COLUMN tenants.max_users IS 'Максимальное количество пользователей';
COMMENT ON COLUMN tenants.max_clients IS 'Максимальное количество клиентов';
COMMENT ON COLUMN tenants.settings IS 'Настройки арендатора в формате JSON';
