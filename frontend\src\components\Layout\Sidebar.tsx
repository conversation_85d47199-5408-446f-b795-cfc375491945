import React from 'react'
import { NavLink } from 'react-router-dom'
import {
  Home,
  Users,
  Phone,
  Hash,
  Ticket,
  CreditCard,
  Settings,
  BarChart3,
  Building2,
  UserCog,
  FileText,
  Shield,
  Zap,
  TrendingUp,
  PieChart,
  PhoneCall
} from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { cn } from '@/lib/utils'

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles: string[]
}

const navigation: NavItem[] = [
  {
    name: 'Дашборд',
    href: '/',
    icon: Home,
    roles: ['provider', 'reseller', 'admin', 'support', 'staff']
  },
  {
    name: 'Арендаторы',
    href: '/tenants',
    icon: Building2,
    roles: ['provider']
  },
  {
    name: 'Пользователи',
    href: '/users',
    icon: UserCog,
    roles: ['provider', 'reseller', 'admin']
  },
  {
    name: 'Роли',
    href: '/roles',
    icon: Shield,
    roles: ['provider', 'admin']
  },
  {
    name: 'Клиенты',
    href: '/clients',
    icon: Users,
    roles: ['provider', 'reseller', 'admin', 'support', 'staff']
  },
  {
    name: 'SIP Аккаунты',
    href: '/sip-accounts',
    icon: Phone,
    roles: ['provider', 'reseller', 'admin', 'support']
  },
  {
    name: 'DID Номера',
    href: '/did-numbers',
    icon: Hash,
    roles: ['provider', 'reseller', 'admin', 'support']
  },
  {
    name: 'Звонки',
    href: '/calls',
    icon: PhoneCall,
    roles: ['provider', 'reseller', 'admin', 'support', 'staff', 'client']
  },
  {
    name: 'Тикеты',
    href: '/tickets',
    icon: Ticket,
    roles: ['provider', 'reseller', 'admin', 'support', 'staff']
  },
  {
    name: 'Биллинг',
    href: '/billing',
    icon: CreditCard,
    roles: ['provider', 'reseller', 'admin']
  },
  {
    name: 'Отчеты',
    href: '/reports',
    icon: FileText,
    roles: ['provider', 'reseller', 'admin']
  },
  {
    name: 'Аналитика',
    href: '/analytics',
    icon: TrendingUp,
    roles: ['provider', 'reseller', 'admin']
  },
  {
    name: 'Логи',
    href: '/logs',
    icon: FileText,
    roles: ['provider', 'reseller', 'admin']
  },
  {
    name: 'Интеграции',
    href: '/integrations',
    icon: Zap,
    roles: ['provider', 'admin']
  },
  {
    name: 'Настройки',
    href: '/settings',
    icon: Settings,
    roles: ['provider', 'reseller', 'admin']
  }
]

export const Sidebar: React.FC = () => {
  const { user } = useAuthStore()

  if (!user) return null

  const filteredNavigation = navigation.filter(item => 
    item.roles.includes(user.role)
  )

  return (
    <div className="flex flex-col w-64 bg-gray-800">
      <div className="flex items-center h-16 px-4 bg-gray-900">
        <h1 className="text-xl font-bold text-white">SPaaS Platform</h1>
      </div>
      
      <div className="flex-1 px-2 py-4 space-y-1">
        {filteredNavigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              cn(
                'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              )
            }
          >
            <item.icon
              className="mr-3 h-5 w-5 flex-shrink-0"
              aria-hidden="true"
            />
            {item.name}
          </NavLink>
        ))}
      </div>

      {/* User info */}
      <div className="flex-shrink-0 p-4 bg-gray-700">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 rounded-full bg-gray-500 flex items-center justify-center">
              <span className="text-sm font-medium text-white">
                {user.first_name?.[0] || user.email[0].toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-white">
              {user.first_name} {user.last_name}
            </p>
            <p className="text-xs text-gray-400 capitalize">
              {user.role}
            </p>
          </div>
        </div>
        {user.tenant && (
          <div className="mt-2">
            <p className="text-xs text-gray-400 truncate">
              {user.tenant.name}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
