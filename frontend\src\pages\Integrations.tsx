import React, { useState, useEffect } from 'react'
import { Zap, Plus, Setting<PERSON>, Check, X, AlertCircle, ExternalLink, Key, Shield } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate } from '@/lib/utils'
import { IntegrationConfigModal } from '@/components/IntegrationConfigModal'

interface Integration {
  id: string
  name: string
  display_name: string
  description: string
  category: 'telephony' | 'messaging' | 'payment'
  status: 'connected' | 'disconnected' | 'error' | 'pending'
  icon: string
  color: string
  website: string
  features: string[]
  config?: {
    [key: string]: any
  }
  connected_at?: string
  last_sync?: string
  error_message?: string
}

// Демо данные интеграций
const demoIntegrations: Integration[] = [
  {
    id: 'twilio',
    name: 'twilio',
    display_name: 'Twilio',
    description: 'Облачная платформа для голосовых вызовов, SMS и видео',
    category: 'telephony',
    status: 'connected',
    icon: '📞',
    color: 'bg-red-500',
    website: 'https://twilio.com',
    features: ['Голосовые вызовы', 'SMS', 'MMS', 'WhatsApp Business', 'Видеозвонки'],
    config: {
      account_sid: 'AC*********************',
      auth_token: '*********************',
      phone_number: '+**********',
      webhook_url: 'https://api.spaas.com/webhooks/twilio'
    },
    connected_at: '2024-01-15T10:30:00Z',
    last_sync: '2024-12-01T15:30:00Z'
  },
  {
    id: 'signalwire',
    name: 'signalwire',
    display_name: 'SignalWire',
    description: 'Современная платформа для коммуникаций и API',
    category: 'telephony',
    status: 'disconnected',
    icon: '🌊',
    color: 'bg-blue-500',
    website: 'https://signalwire.com',
    features: ['Голосовые вызовы', 'SMS', 'Видео', 'WebRTC', 'AI голос'],
    config: {},
    connected_at: undefined,
    last_sync: undefined
  },
  {
    id: 'viber',
    name: 'viber',
    display_name: 'Viber Business',
    description: 'Бизнес-мессенджер для общения с клиентами',
    category: 'messaging',
    status: 'error',
    icon: '💜',
    color: 'bg-purple-500',
    website: 'https://viber.com/business',
    features: ['Сообщения', 'Медиа файлы', 'Кнопки', 'Карусели', 'Боты'],
    config: {
      auth_token: '*********************',
      webhook_url: 'https://api.spaas.com/webhooks/viber'
    },
    connected_at: '2024-02-01T09:00:00Z',
    last_sync: '2024-11-28T12:00:00Z',
    error_message: 'Недействительный токен авторизации'
  },
  {
    id: 'telegram',
    name: 'telegram',
    display_name: 'Telegram Bot API',
    description: 'Создание ботов для автоматизации общения',
    category: 'messaging',
    status: 'connected',
    icon: '✈️',
    color: 'bg-blue-400',
    website: 'https://core.telegram.org/bots',
    features: ['Сообщения', 'Inline клавиатуры', 'Файлы', 'Группы', 'Каналы'],
    config: {
      bot_token: '*********************',
      webhook_url: 'https://api.spaas.com/webhooks/telegram',
      bot_username: '@spaas_support_bot'
    },
    connected_at: '2024-01-20T14:15:00Z',
    last_sync: '2024-12-01T15:25:00Z'
  },
  {
    id: 'stripe',
    name: 'stripe',
    display_name: 'Stripe',
    description: 'Платежная система для онлайн-платежей',
    category: 'payment',
    status: 'connected',
    icon: '💳',
    color: 'bg-indigo-500',
    website: 'https://stripe.com',
    features: ['Карточные платежи', 'Подписки', 'Возвраты', 'Аналитика', 'Мультивалютность'],
    config: {
      publishable_key: 'pk_*********************',
      secret_key: 'sk_*********************',
      webhook_secret: 'whsec_*********************',
      webhook_url: 'https://api.spaas.com/webhooks/stripe'
    },
    connected_at: '2024-01-10T11:45:00Z',
    last_sync: '2024-12-01T15:20:00Z'
  },
  {
    id: 'square',
    name: 'square',
    display_name: 'Square',
    description: 'Платежная система и POS-решения',
    category: 'payment',
    status: 'pending',
    icon: '⬜',
    color: 'bg-gray-700',
    website: 'https://squareup.com',
    features: ['Платежи', 'Инвентарь', 'Аналитика', 'Лояльность', 'Подарочные карты'],
    config: {
      application_id: 'sq*********************',
      access_token: '*********************'
    },
    connected_at: '2024-11-30T16:00:00Z',
    last_sync: undefined
  }
]

export const Integrations: React.FC = () => {
  const { user } = useAuthStore()
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null)
  const [showConfigModal, setShowConfigModal] = useState(false)

  useEffect(() => {
    fetchIntegrations()
  }, [user])

  const fetchIntegrations = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setIntegrations(demoIntegrations)
      } else {
        // Здесь будет реальный запрос к Supabase
        setIntegrations([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredIntegrations = integrations.filter(integration => 
    selectedCategory === 'all' || integration.category === selectedCategory
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Check className="h-4 w-4 text-green-600" />
      case 'disconnected':
        return <X className="h-4 w-4 text-gray-400" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      default:
        return <X className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      connected: { label: 'Подключено', className: 'badge-success' },
      disconnected: { label: 'Отключено', className: 'badge-secondary' },
      error: { label: 'Ошибка', className: 'badge-danger' },
      pending: { label: 'Ожидание', className: 'badge-warning' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.disconnected
    
    return (
      <span className={`badge ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const getCategoryName = (category: string) => {
    const categories = {
      telephony: 'Телефония',
      messaging: 'Мессенджеры',
      payment: 'Платежи'
    }
    return categories[category as keyof typeof categories] || category
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'telephony':
        return '📞'
      case 'messaging':
        return '💬'
      case 'payment':
        return '💳'
      default:
        return '🔌'
    }
  }

  const handleConnect = (integration: Integration) => {
    setSelectedIntegration(integration)
    setShowConfigModal(true)
  }

  const handleDisconnect = (integrationId: string) => {
    if (confirm('Вы уверены, что хотите отключить эту интеграцию?')) {
      setIntegrations(prev => prev.map(int =>
        int.id === integrationId
          ? { ...int, status: 'disconnected', connected_at: undefined, last_sync: undefined }
          : int
      ))
    }
  }

  const handleSaveConfig = (integration: Integration, config: any) => {
    setIntegrations(prev => prev.map(int =>
      int.id === integration.id
        ? {
            ...int,
            config,
            status: 'connected',
            connected_at: new Date().toISOString(),
            last_sync: new Date().toISOString(),
            error_message: undefined
          }
        : int
    ))
  }

  const getStats = () => {
    const connected = integrations.filter(i => i.status === 'connected').length
    const errors = integrations.filter(i => i.status === 'error').length
    const pending = integrations.filter(i => i.status === 'pending').length
    
    return { connected, errors, pending, total: integrations.length }
  }

  const stats = getStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Интеграции</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление внешними сервисами и API
          </p>
        </div>
        <button className="btn btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Добавить интеграцию
        </button>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon green">
            <Check className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Подключено</h3>
            <p>{stats.connected}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon red">
            <AlertCircle className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Ошибки</h3>
            <p>{stats.errors}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <AlertCircle className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Ожидание</h3>
            <p>{stats.pending}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon blue">
            <Zap className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего</h3>
            <p>{stats.total}</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-header">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="form-input"
              >
                <option value="all">Все категории</option>
                <option value="telephony">📞 Телефония</option>
                <option value="messaging">💬 Мессенджеры</option>
                <option value="payment">💳 Платежи</option>
              </select>
            </div>
          </div>
        </div>

        {/* Integration Cards */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredIntegrations.map((integration) => (
              <div key={integration.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className={`w-12 h-12 ${integration.color} rounded-lg flex items-center justify-center text-white text-xl mr-3`}>
                      {integration.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {integration.display_name}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500">
                        {getCategoryIcon(integration.category)}
                        <span className="ml-1">{getCategoryName(integration.category)}</span>
                      </div>
                    </div>
                  </div>
                  {getStatusIcon(integration.status)}
                </div>

                <p className="text-sm text-gray-600 mb-4">
                  {integration.description}
                </p>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Статус</span>
                    {getStatusBadge(integration.status)}
                  </div>
                  
                  {integration.connected_at && (
                    <div className="text-xs text-gray-500">
                      Подключено: {formatDate(integration.connected_at, 'short')}
                    </div>
                  )}
                  
                  {integration.last_sync && (
                    <div className="text-xs text-gray-500">
                      Синхронизация: {formatDate(integration.last_sync, 'short')}
                    </div>
                  )}

                  {integration.error_message && (
                    <div className="text-xs text-red-600 mt-1">
                      {integration.error_message}
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Возможности</div>
                  <div className="flex flex-wrap gap-1">
                    {integration.features.slice(0, 3).map((feature, index) => (
                      <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {feature}
                      </span>
                    ))}
                    {integration.features.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{integration.features.length - 3} еще
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex gap-2">
                  {integration.status === 'connected' ? (
                    <>
                      <button
                        onClick={() => handleConnect(integration)}
                        className="flex-1 btn btn-secondary text-sm"
                      >
                        <Settings className="h-3 w-3 mr-1" />
                        Настроить
                      </button>
                      <button
                        onClick={() => handleDisconnect(integration.id)}
                        className="flex-1 btn btn-danger text-sm"
                      >
                        Отключить
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => handleConnect(integration)}
                      className="flex-1 btn btn-primary text-sm"
                    >
                      <Zap className="h-3 w-3 mr-1" />
                      Подключить
                    </button>
                  )}
                  
                  <a
                    href={integration.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-gray-400 hover:text-gray-600"
                    title="Открыть сайт"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </div>
              </div>
            ))}
          </div>

          {filteredIntegrations.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {selectedCategory !== 'all' 
                  ? 'Интеграции не найдены в выбранной категории'
                  : 'Пока нет интеграций'
                }
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Модальное окно конфигурации */}
      <IntegrationConfigModal
        isOpen={showConfigModal}
        onClose={() => {
          setShowConfigModal(false)
          setSelectedIntegration(null)
        }}
        onSave={handleSaveConfig}
        integration={selectedIntegration}
      />
    </div>
  )
}
