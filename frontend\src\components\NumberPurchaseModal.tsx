import React, { useState, useEffect } from 'react'
import { Search, Phone, MapPin, Globe, Star, ShoppingCart, Filter, X } from 'lucide-react'
import { Modal, ModalFooter } from '@/components/Modal'
import { formatCurrency } from '@/lib/utils'

interface AvailableNumber {
  number: string
  country_code: string
  city: string
  region: string
  number_type: string
  capabilities: string[]
  monthly_cost: number
  setup_cost: number
  provider: string
  is_premium?: boolean
}

interface NumberPurchaseModalProps {
  isOpen: boolean
  onClose: () => void
  onPurchase: (numbers: AvailableNumber[], notes: string) => void
}

// Демо данные доступных номеров
const availableNumbers: AvailableNumber[] = [
  {
    number: '+****************',
    country_code: 'US',
    city: 'Jersey City',
    region: 'New Jersey',
    number_type: 'local',
    capabilities: ['voice', 'sms'],
    monthly_cost: 1500,
    setup_cost: 0,
    provider: 'Twilio'
  },
  {
    number: '+****************',
    country_code: 'US',
    city: 'Jersey City',
    region: 'New Jersey',
    number_type: 'local',
    capabilities: ['voice', 'sms', 'mms'],
    monthly_cost: 1800,
    setup_cost: 500,
    provider: 'Twilio'
  },
  {
    number: '+7 (495) 123-45-67',
    country_code: 'RU',
    city: 'Москва',
    region: 'Московская область',
    number_type: 'local',
    capabilities: ['voice'],
    monthly_cost: 2500,
    setup_cost: 1000,
    provider: 'Rostelecom'
  },
  {
    number: '+7 (812) 987-65-43',
    country_code: 'RU',
    city: 'Санкт-Петербург',
    region: 'Ленинградская область',
    number_type: 'local',
    capabilities: ['voice', 'sms'],
    monthly_cost: 2200,
    setup_cost: 800,
    provider: 'MTS'
  },
  {
    number: '+****************',
    country_code: 'US',
    city: 'Toll Free',
    region: 'National',
    number_type: 'toll_free',
    capabilities: ['voice'],
    monthly_cost: 3000,
    setup_cost: 1500,
    provider: 'Bandwidth',
    is_premium: true
  },
  {
    number: '+44 20 7946 0958',
    country_code: 'GB',
    city: 'London',
    region: 'Greater London',
    number_type: 'local',
    capabilities: ['voice', 'sms'],
    monthly_cost: 2800,
    setup_cost: 1200,
    provider: 'Vonage'
  }
]

export const NumberPurchaseModal: React.FC<NumberPurchaseModalProps> = ({
  isOpen,
  onClose,
  onPurchase
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [countryFilter, setCountryFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [selectedNumbers, setSelectedNumbers] = useState<AvailableNumber[]>([])
  const [notes, setNotes] = useState('')

  const filteredNumbers = availableNumbers.filter(number => {
    const matchesSearch = 
      number.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      number.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      number.region.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCountry = countryFilter === 'all' || number.country_code === countryFilter
    const matchesType = typeFilter === 'all' || number.number_type === typeFilter

    return matchesSearch && matchesCountry && matchesType
  })

  const getCountryFlag = (countryCode: string) => {
    const flags: Record<string, string> = {
      'RU': '🇷🇺',
      'US': '🇺🇸',
      'GB': '🇬🇧',
      'DE': '🇩🇪',
      'FR': '🇫🇷'
    }
    return flags[countryCode] || '🌍'
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'local':
        return <MapPin className="h-4 w-4 text-blue-600" />
      case 'toll_free':
        return <Phone className="h-4 w-4 text-green-600" />
      case 'mobile':
        return <Phone className="h-4 w-4 text-purple-600" />
      default:
        return <Globe className="h-4 w-4 text-gray-600" />
    }
  }

  const getTypeDisplayName = (type: string) => {
    const types: Record<string, string> = {
      'local': 'Местный',
      'toll_free': 'Бесплатный',
      'mobile': 'Мобильный',
      'shortcode': 'Короткий'
    }
    return types[type] || type
  }

  const getCapabilityIcon = (capability: string) => {
    switch (capability) {
      case 'voice':
        return '📞'
      case 'sms':
        return '💬'
      case 'mms':
        return '📷'
      case 'fax':
        return '📠'
      default:
        return '❓'
    }
  }

  const toggleNumberSelection = (number: AvailableNumber) => {
    setSelectedNumbers(prev => {
      const isSelected = prev.some(n => n.number === number.number)
      if (isSelected) {
        return prev.filter(n => n.number !== number.number)
      } else {
        return [...prev, number]
      }
    })
  }

  const getTotalCost = () => {
    const setupCost = selectedNumbers.reduce((sum, n) => sum + n.setup_cost, 0)
    const monthlyCost = selectedNumbers.reduce((sum, n) => sum + n.monthly_cost, 0)
    return { setupCost, monthlyCost }
  }

  const handlePurchase = () => {
    if (selectedNumbers.length > 0) {
      onPurchase(selectedNumbers, notes)
      setSelectedNumbers([])
      setNotes('')
      onClose()
    }
  }

  const handleClose = () => {
    setSelectedNumbers([])
    setNotes('')
    onClose()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Покупка DID номеров"
      size="xl"
    >
      <div className="space-y-6">
        {/* Фильтры */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="search-box">
              <Search className="search-icon" />
              <input
                type="text"
                placeholder="Поиск номеров..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          <div className="sm:w-48">
            <select
              value={countryFilter}
              onChange={(e) => setCountryFilter(e.target.value)}
              className="form-input"
            >
              <option value="all">Все страны</option>
              <option value="RU">🇷🇺 Россия</option>
              <option value="US">🇺🇸 США</option>
              <option value="GB">🇬🇧 Великобритания</option>
            </select>
          </div>

          <div className="sm:w-48">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="form-input"
            >
              <option value="all">Все типы</option>
              <option value="local">Местные</option>
              <option value="toll_free">Бесплатные</option>
              <option value="mobile">Мобильные</option>
            </select>
          </div>
        </div>

        {/* Список номеров */}
        <div className="max-h-96 overflow-y-auto border rounded-lg">
          <table className="table">
            <thead className="sticky top-0 bg-gray-50">
              <tr>
                <th className="w-12"></th>
                <th>Номер</th>
                <th>Местоположение</th>
                <th>Тип</th>
                <th>Возможности</th>
                <th>Стоимость</th>
                <th>Провайдер</th>
              </tr>
            </thead>
            <tbody>
              {filteredNumbers.map((number) => (
                <tr 
                  key={number.number}
                  className={`cursor-pointer hover:bg-gray-50 ${
                    selectedNumbers.some(n => n.number === number.number) ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => toggleNumberSelection(number)}
                >
                  <td>
                    <input
                      type="checkbox"
                      checked={selectedNumbers.some(n => n.number === number.number)}
                      onChange={() => toggleNumberSelection(number)}
                      className="rounded"
                    />
                  </td>
                  <td>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="font-medium text-gray-900">
                          {number.number}
                        </div>
                        {number.is_premium && (
                          <div className="flex items-center text-xs text-yellow-600">
                            <Star className="h-3 w-3 mr-1" />
                            Премиум
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <span className="mr-2">{getCountryFlag(number.country_code)}</span>
                      <div>
                        <div className="text-sm text-gray-900">{number.city}</div>
                        <div className="text-xs text-gray-500">{number.region}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getTypeIcon(number.number_type)}
                      <span className="ml-2 text-sm">
                        {getTypeDisplayName(number.number_type)}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="flex gap-1">
                      {number.capabilities.map((cap) => (
                        <span key={cap} title={cap} className="text-sm">
                          {getCapabilityIcon(cap)}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td>
                    <div>
                      <div className="text-sm font-medium">
                        {formatCurrency(number.monthly_cost)}/мес
                      </div>
                      {number.setup_cost > 0 && (
                        <div className="text-xs text-gray-500">
                          Подключение: {formatCurrency(number.setup_cost)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="text-sm text-gray-500">
                    {number.provider}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredNumbers.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                Номера не найдены по заданным критериям
              </div>
            </div>
          )}
        </div>

        {/* Выбранные номера */}
        {selectedNumbers.length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              Выбрано номеров: {selectedNumbers.length}
            </h4>
            <div className="space-y-2">
              {selectedNumbers.map((number) => (
                <div key={number.number} className="flex items-center justify-between text-sm">
                  <span>{number.number} ({number.city})</span>
                  <div className="flex items-center gap-2">
                    <span>{formatCurrency(number.monthly_cost)}/мес</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleNumberSelection(number)
                      }}
                      className="text-red-600 hover:text-red-800"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t border-blue-200">
              <div className="flex justify-between text-sm">
                <span>Подключение:</span>
                <span className="font-medium">{formatCurrency(getTotalCost().setupCost)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Ежемесячно:</span>
                <span className="font-medium">{formatCurrency(getTotalCost().monthlyCost)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Примечания */}
        <div className="form-group">
          <label className="form-label">
            Примечания к покупке
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="form-input"
            rows={3}
            placeholder="Укажите цель покупки, особые требования..."
          />
        </div>
      </div>

      <ModalFooter>
        <button
          type="button"
          onClick={handleClose}
          className="btn btn-secondary"
        >
          Отмена
        </button>
        <button
          onClick={handlePurchase}
          disabled={selectedNumbers.length === 0}
          className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          Купить ({selectedNumbers.length})
        </button>
      </ModalFooter>
    </Modal>
  )
}
