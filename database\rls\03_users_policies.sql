-- RLS политики для таблицы users

-- Provider может видеть и управлять всеми пользователями
CREATE POLICY "Provider full access to users" ON users
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи могут видеть других пользователей своего арендатора
CREATE POLICY "Users can view tenant users" ON users
  FOR SELECT 
  USING (
    auth.has_tenant_access(tenant_id)
  );

-- Администраторы арендатора могут управлять пользователями своего арендатора
CREATE POLICY "Tenant admins can manage users" ON users
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  );

-- Пользователи могут обновлять свой собственный профиль
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE 
  USING (
    user_id = auth.user_id()
  )
  WITH CHECK (
    user_id = auth.user_id()
    -- Ограничиваем поля, которые пользователь может изменить
    AND (
      OLD.role = NEW.role -- роль не может быть изменена самим пользователем
      AND OLD.tenant_id = NEW.tenant_id -- tenant_id не может быть изменен
      AND OLD.is_active = NEW.is_active -- статус активности не может быть изменен
    )
  );

-- Support и Staff могут видеть пользователей, но не изменять
CREATE POLICY "Support staff can view users" ON users
  FOR SELECT 
  USING (
    auth.user_role() IN ('support', 'staff')
    AND tenant_id = auth.user_tenant_id()
  );

-- Клиенты могут видеть только свой профиль
CREATE POLICY "Clients can view own profile" ON users
  FOR SELECT 
  USING (
    auth.user_role() = 'client'
    AND user_id = auth.user_id()
  );

-- Запрет на создание пользователей для не-администраторов
CREATE POLICY "Only admins can create users" ON users
  FOR INSERT 
  WITH CHECK (
    auth.is_provider() 
    OR (
      auth.is_tenant_admin()
      AND tenant_id = auth.user_tenant_id()
    )
  );

-- Запрет на удаление пользователей для не-администраторов
CREATE POLICY "Only admins can delete users" ON users
  FOR DELETE 
  USING (
    auth.is_provider() 
    OR (
      auth.is_tenant_admin()
      AND tenant_id = auth.user_tenant_id()
      AND user_id != auth.user_id() -- нельзя удалить самого себя
    )
  );

-- Комментарии к политикам
COMMENT ON POLICY "Provider full access to users" ON users IS 
  'Provider имеет полный доступ ко всем пользователям';

COMMENT ON POLICY "Users can view tenant users" ON users IS 
  'Пользователи могут просматривать других пользователей своего арендатора';

COMMENT ON POLICY "Tenant admins can manage users" ON users IS 
  'Администраторы арендатора могут управлять пользователями своего арендатора';

COMMENT ON POLICY "Users can update own profile" ON users IS 
  'Пользователи могут обновлять свой собственный профиль (ограниченные поля)';

COMMENT ON POLICY "Support staff can view users" ON users IS 
  'Support и Staff могут просматривать пользователей, но не изменять';

COMMENT ON POLICY "Clients can view own profile" ON users IS 
  'Клиенты могут видеть только свой собственный профиль';
