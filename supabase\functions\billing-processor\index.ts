import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface BillingRequest {
  action: 'generate_invoice' | 'process_payment' | 'calculate_usage'
  tenant_id: string
  client_id?: string
  period_start?: string
  period_end?: string
  payment_data?: {
    invoice_id: string
    amount: number
    payment_method: string
    reference_number?: string
  }
}

interface UsageData {
  sip_account_id: string
  outgoing_minutes: number
  incoming_minutes: number
  monthly_fee: number
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, tenant_id, client_id, period_start, period_end, payment_data } = await req.json() as BillingRequest

    // Проверяем авторизацию
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    switch (action) {
      case 'generate_invoice':
        return await generateInvoice(supabaseClient, tenant_id, client_id!, period_start!, period_end!)
      
      case 'process_payment':
        return await processPayment(supabaseClient, payment_data!)
      
      case 'calculate_usage':
        return await calculateUsage(supabaseClient, tenant_id, client_id!, period_start!, period_end!)
      
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }

  } catch (error) {
    console.error('Billing processor error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function generateInvoice(supabaseClient: any, tenant_id: string, client_id: string, period_start: string, period_end: string) {
  try {
    // Получаем информацию о клиенте
    const { data: client, error: clientError } = await supabaseClient
      .from('clients')
      .select('*')
      .eq('client_id', client_id)
      .eq('tenant_id', tenant_id)
      .single()

    if (clientError || !client) {
      return new Response(
        JSON.stringify({ error: 'Client not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Рассчитываем использование
    const usageResponse = await calculateUsage(supabaseClient, tenant_id, client_id, period_start, period_end)
    const usageData = await usageResponse.json()

    if (!usageData.success) {
      return usageResponse
    }

    // Генерируем номер инвойса
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`

    // Создаем инвойс
    const { data: invoice, error: invoiceError } = await supabaseClient
      .from('invoices')
      .insert({
        tenant_id,
        client_id,
        invoice_number: invoiceNumber,
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 14 дней
        status: 'draft',
        subtotal: usageData.total_amount,
        tax_amount: usageData.total_amount * 0.2, // 20% НДС
        total_amount: usageData.total_amount * 1.2,
        currency: 'RUB',
        payment_terms: 'Оплата в течение 14 дней'
      })
      .select()
      .single()

    if (invoiceError) {
      return new Response(
        JSON.stringify({ error: 'Failed to create invoice' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Добавляем позиции инвойса
    const invoiceItems = []
    
    // Ежемесячная плата
    if (usageData.monthly_fees > 0) {
      invoiceItems.push({
        invoice_id: invoice.invoice_id,
        description: 'Ежемесячная абонентская плата',
        quantity: 1,
        unit_price: usageData.monthly_fees,
        total_price: usageData.monthly_fees,
        service_type: 'monthly_fee',
        service_period_start: period_start,
        service_period_end: period_end
      })
    }

    // Исходящие звонки
    if (usageData.call_charges > 0) {
      invoiceItems.push({
        invoice_id: invoice.invoice_id,
        description: `Исходящие звонки (${usageData.total_minutes} мин)`,
        quantity: usageData.total_minutes,
        unit_price: usageData.per_minute_rate,
        total_price: usageData.call_charges,
        service_type: 'call_charges',
        service_period_start: period_start,
        service_period_end: period_end
      })
    }

    if (invoiceItems.length > 0) {
      await supabaseClient
        .from('invoice_items')
        .insert(invoiceItems)
    }

    // Логируем создание инвойса
    await supabaseClient
      .from('activity_logs')
      .insert({
        tenant_id,
        user_id: null, // Системное действие
        action: 'CREATE',
        table_name: 'invoices',
        record_id: invoice.invoice_id,
        changes: { invoice_number: invoiceNumber, total_amount: invoice.total_amount }
      })

    return new Response(
      JSON.stringify({
        success: true,
        invoice: {
          ...invoice,
          items: invoiceItems
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Generate invoice error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to generate invoice' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function processPayment(supabaseClient: any, payment_data: any) {
  try {
    // Генерируем номер платежа
    const paymentNumber = `PAY-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`

    // Получаем информацию об инвойсе
    const { data: invoice, error: invoiceError } = await supabaseClient
      .from('invoices')
      .select('*')
      .eq('invoice_id', payment_data.invoice_id)
      .single()

    if (invoiceError || !invoice) {
      return new Response(
        JSON.stringify({ error: 'Invoice not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Создаем платеж
    const { data: payment, error: paymentError } = await supabaseClient
      .from('payments')
      .insert({
        tenant_id: invoice.tenant_id,
        client_id: invoice.client_id,
        invoice_id: payment_data.invoice_id,
        payment_number: paymentNumber,
        payment_date: new Date().toISOString().split('T')[0],
        amount: payment_data.amount,
        currency: 'RUB',
        payment_method: payment_data.payment_method,
        status: 'completed',
        reference_number: payment_data.reference_number
      })
      .select()
      .single()

    if (paymentError) {
      return new Response(
        JSON.stringify({ error: 'Failed to create payment' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Обновляем статус инвойса
    if (payment_data.amount >= invoice.total_amount) {
      await supabaseClient
        .from('invoices')
        .update({ status: 'paid' })
        .eq('invoice_id', payment_data.invoice_id)
    }

    // Обновляем баланс клиента
    await supabaseClient
      .from('clients')
      .update({ 
        current_balance: supabaseClient.raw(`current_balance + ${payment_data.amount}`)
      })
      .eq('client_id', invoice.client_id)

    return new Response(
      JSON.stringify({
        success: true,
        payment
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Process payment error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to process payment' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function calculateUsage(supabaseClient: any, tenant_id: string, client_id: string, period_start: string, period_end: string) {
  try {
    // Получаем SIP аккаунты клиента
    const { data: sipAccounts, error: sipError } = await supabaseClient
      .from('sip_accounts')
      .select('*')
      .eq('tenant_id', tenant_id)
      .eq('client_id', client_id)

    if (sipError) {
      return new Response(
        JSON.stringify({ error: 'Failed to fetch SIP accounts' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Рассчитываем использование (в реальной системе здесь будут запросы к CDR)
    let totalMinutes = 0
    let monthlyFees = 0
    const perMinuteRate = 2.0 // 2 рубля за минуту

    // Для каждого SIP аккаунта
    for (const account of sipAccounts || []) {
      // Ежемесячная плата (в демо режиме фиксированная)
      monthlyFees += 1000 // 1000 рублей за аккаунт

      // Минуты звонков (в демо режиме случайные)
      const accountMinutes = Math.floor(Math.random() * 500) + 100
      totalMinutes += accountMinutes
    }

    const callCharges = totalMinutes * perMinuteRate
    const totalAmount = monthlyFees + callCharges

    return new Response(
      JSON.stringify({
        success: true,
        monthly_fees: monthlyFees,
        total_minutes: totalMinutes,
        per_minute_rate: perMinuteRate,
        call_charges: callCharges,
        total_amount: totalAmount,
        period_start,
        period_end
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Calculate usage error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to calculate usage' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}
