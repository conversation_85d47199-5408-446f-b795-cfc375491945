# 📞 Система звонков с WebRTC

## Обзор

Реализована полная система звонков с интеграцией SignalWire WebRTC SDK для совершения голосовых и видеозвонков прямо из браузера.

## ✨ Новые возможности

### 1. WebRTC звонки
- **Голосовые звонки** через SignalWire
- **Видеозвонки** с поддержкой камеры
- **Управление звонком** - отключение звука, динамика, видео
- **Входящие и исходящие** звонки

### 2. Интерфейс звонков
- **Набор номера** с виртуальной клавиатурой
- **История звонков** с фильтрацией
- **Контакты** с быстрым набором
- **Активный звонок** с полным управлением

### 3. Настройки WebRTC
- **Конфигурация SignalWire** (Project ID, Token, Space)
- **Настройки аудио** (микрофон, динамики, фильтры)
- **Настройки видео** (камера, разрешение, FPS)
- **Сетевые настройки** (кодеки, пропускная способность)

## 🎯 Интерфейс

### Главная страница звонков
```
┌─────────────────────────────────────────┐
│ 📞 Звонки                [🟢 Подключен] │
│ WebRTC звонки через SignalWire [⚙️ Настр]│
├─────────────────────────────────────────┤
│ [📞 Набор] [📋 История] [👥 Контакты]   │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ [+1234567890____________]           │ │
│ │                                     │ │
│ │ [1] [2] [3]                        │ │
│ │ [4] [5] [6]                        │ │
│ │ [7] [8] [9]                        │ │
│ │ [*] [0] [#]                        │ │
│ │                                     │ │
│ │ [Очистить] [📞 Позвонить]          │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### Активный звонок
```
┌─────────────────────────────────────────┐
│ 📞 Звонки                [🟢 Подключен] │
├─────────────────────────────────────────┤
│              [👤 Аватар]                │
│           Иван Петров                   │
│          +1987654321                    │
│        Исходящий звонок                 │
│                                         │
│            02:45                        │
│                                         │
│ ┌─────────────┐ ┌─────────────┐         │
│ │📹 Удаленное │ │📷 Локальное │         │
│ │    видео    │ │    видео    │         │
│ └─────────────┘ └─────────────┘         │
│                                         │
│ [🎤] [🔊] [📹] [📞]                     │
│ Мут  Дин  Вид  Завершить               │
└─────────────────────────────────────────┘
```

### Настройки WebRTC
```
┌─────────────────────────────────────────┐
│ ⚙️ Настройки WebRTC                     │
├─────────────────────────────────────────┤
│ 📡 Подключение SignalWire               │
│ Project ID: [xxxxxxxx-xxxx-xxxx...]     │
│ Auth Token: [PT••••••••••••••••••••]    │
│ Space URL:  [example.signalwire.com]    │
│                                         │
│ 🎤 Настройки аудио                      │
│ Микрофон:  [По умолчанию ▼]             │
│ Динамики:  [По умолчанию ▼]             │
│ ☑️ Подавление эха                       │
│ ☑️ Подавление шума                      │
│ ☑️ Автоматическая регулировка           │
│                                         │
│ 📹 Настройки видео                      │
│ ☑️ Включить видеозвонки                 │
│ Камера:     [По умолчанию ▼]            │
│ Разрешение: [720p ▼]                    │
│ Частота:    [30 FPS ▼]                  │
│                                         │
│ 🛡️ Сетевые настройки                   │
│ Пропускная способность: [1000] kbps     │
│ Аудио кодек: [Opus ▼]                   │
│                                         │
│ 🧪 Тестирование устройств               │
│ ✅ Микрофон: Работает                   │
│ ✅ Камера: Работает                     │
│ ✅ Подключение: Успешно                 │
│                                         │
│ [Отмена] [⚙️ Сохранить]                 │
└─────────────────────────────────────────┘
```

## 🔧 Технические детали

### Новые компоненты
- `Calls.tsx` - основная страница звонков
- `WebRTCSettings.tsx` - настройки WebRTC

### Интеграция SignalWire

#### Подключение
```typescript
// Инициализация SignalWire WebRTC SDK
import { SignalWire } from '@signalwire/js'

const client = new SignalWire.WebRTC({
  project: 'your-project-id',
  token: 'your-auth-token',
  space: 'your-space.signalwire.com'
})
```

#### Исходящие звонки
```typescript
const call = await client.call({
  to: '+1234567890',
  from: '+1987654321'
})
```

#### Входящие звонки
```typescript
client.on('call.received', (call) => {
  // Обработка входящего звонка
  call.answer()
})
```

### Структура данных

#### История звонков
```typescript
interface CallHistory {
  call_id: string
  direction: 'inbound' | 'outbound'
  from: string
  to: string
  duration: number
  status: 'completed' | 'missed' | 'busy' | 'failed'
  started_at: string
  ended_at?: string
}
```

#### Контакты
```typescript
interface Contact {
  contact_id: string
  name: string
  phone: string
  company?: string
  type: 'client' | 'internal' | 'external'
}
```

#### Конфигурация WebRTC
```typescript
interface WebRTCConfig {
  signalwire: {
    project: string
    token: string
    space: string
  }
  audio: {
    inputDevice: string
    outputDevice: string
    echoCancellation: boolean
    noiseSuppression: boolean
    autoGainControl: boolean
  }
  video: {
    inputDevice: string
    resolution: string
    frameRate: number
    enabled: boolean
  }
  network: {
    iceServers: string[]
    bandwidth: number
    codec: string
  }
}
```

## 🎨 UX/UI особенности

### Набор номера
- **Виртуальная клавиатура** для ввода номера
- **Автоформатирование** номеров
- **Быстрый набор** из контактов
- **Валидация номеров**

### Управление звонком
- **Интуитивные кнопки** управления
- **Визуальная обратная связь** состояний
- **Таймер звонка** в реальном времени
- **Статусы подключения**

### История и контакты
- **Поиск и фильтрация** записей
- **Цветовая индикация** статусов
- **Быстрые действия** (перезвонить)
- **Группировка** по типам

## 🚀 Использование

### Настройка SignalWire:
1. Получите учетные данные в SignalWire Console
2. Откройте "Настройки" на странице звонков
3. Введите Project ID, Auth Token и Space URL
4. Протестируйте подключение
5. Сохраните настройки

### Совершение звонка:
1. Перейдите на вкладку "Набор номера"
2. Введите номер телефона
3. Нажмите "Позвонить"
4. Управляйте звонком кнопками
5. Завершите звонок

### Настройка устройств:
1. Откройте "Настройки WebRTC"
2. Выберите микрофон и динамики
3. Настройте параметры аудио
4. При необходимости включите видео
5. Протестируйте устройства

## 🔒 Безопасность

### Шифрование
- **DTLS** для медиа-трафика
- **SRTP** для аудио/видео потоков
- **WSS** для сигнального канала

### Аутентификация
- **JWT токены** SignalWire
- **Проверка разрешений** на звонки
- **Валидация номеров**

### Приватность
- **Локальное хранение** настроек
- **Маскировка токенов** в интерфейсе
- **Контроль доступа** к устройствам

## 📊 Мониторинг

### Статусы подключения
- **Реальное время** состояния WebRTC
- **Индикаторы качества** связи
- **Уведомления** об ошибках

### Метрики звонков
- **Длительность** разговоров
- **Качество** соединения
- **Статистика** использования

## 🔮 Будущие улучшения

### Функциональность
- **Конференц-звонки** (3+ участников)
- **Запись разговоров** с согласием
- **Переадресация** звонков
- **Автоответчик** и голосовая почта

### Интеграции
- **CRM системы** для контекста звонков
- **Календари** для планирования звонков
- **Уведомления** в мессенджерах
- **Аналитика** качества связи

### UX улучшения
- **Горячие клавиши** для управления
- **Темная тема** интерфейса
- **Мобильная оптимизация**
- **Accessibility** поддержка

## 📱 Требования браузера

### Поддерживаемые браузеры:
- **Chrome** 80+ (рекомендуется)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

### Необходимые разрешения:
- **Микрофон** - для голосовых звонков
- **Камера** - для видеозвонков (опционально)
- **Уведомления** - для входящих звонков

### Сетевые требования:
- **HTTPS** обязательно для WebRTC
- **Открытые порты** для RTP/SRTP
- **STUN/TURN** серверы для NAT

## 🆘 Устранение неполадок

### Проблемы с подключением:
1. Проверьте учетные данные SignalWire
2. Убедитесь в наличии HTTPS
3. Проверьте сетевые настройки
4. Перезагрузите страницу

### Проблемы с аудио:
1. Проверьте разрешения браузера
2. Выберите правильные устройства
3. Протестируйте микрофон и динамики
4. Проверьте настройки фильтров

### Проблемы с видео:
1. Убедитесь что видео включено
2. Проверьте разрешения камеры
3. Выберите правильную камеру
4. Проверьте разрешение и FPS

## 📞 Доступность

Страница звонков доступна по адресу `/calls` для всех ролей:
- **Provider** - полный доступ
- **Reseller** - полный доступ
- **Admin** - полный доступ
- **Support** - полный доступ
- **Staff** - полный доступ
- **Client** - полный доступ

Все пользователи могут совершать и принимать звонки в рамках своих разрешений.
