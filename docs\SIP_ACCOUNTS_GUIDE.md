# Руководство по настройке SIP аккаунтов

## 🎯 **Обзор новых возможностей**

Форма создания SIP аккаунтов теперь включает все необходимые параметры для профессиональной настройки VoIP-соединений.

## 📋 **Структура формы**

### **1. 👤 Основные настройки**
- **Клиент** - выбор клиента из списка
- **SIP Username** - уникальный идентификатор (например: 1001)
- **Пароль** - с генератором безопасных паролей
- **Отображаемое имя** - имя для Caller ID
- **Максимум звонков** - лимит одновременных соединений
- **Статус** - активный/неактивный/приостановлен

### **2. 🖥️ Настройки сервера**
- **SIP Сервер** - адрес SIP сервера (sip.example.com)
- **Порт SIP** - обычно 5060 (UDP/TCP) или 5061 (TLS)
- **Протокол** - UDP, TCP или TLS
- **STUN Сервер** - для работы через NAT
- **ICE** - улучшенная связность через NAT

### **3. 🔒 Настройки безопасности**
- **TLS/SSL** - шифрование SIP сигнализации
- **SRTP** - шифрование аудио/видео трафика
- **Имя для аутентификации** - если отличается от username

### **4. 🎵 Кодеки**
**Аудио кодеки:**
- **G.711** - стандартный, высокое качество (рекомендуется)
- **G.722** - HD аудио
- **G.729** - сжатый, экономит трафик
- **Opus** - современный, адаптивный
- **GSM** - совместимость

**Видео кодеки:**
- **H.264** - стандартный (рекомендуется)
- **VP8/VP9** - открытые стандарты
- **H.265** - новый, эффективный

### **5. ⚙️ Дополнительные функции**
- **Переадресация звонков** - с указанием номера
- **Голосовая почта** - автозапись при недоступности
- **IVR** - интерактивное голосовое меню

## 🔧 **Рекомендуемые настройки**

### **Базовая конфигурация:**
```
Сервер: sip.yourprovider.com
Порт: 5060
Протокол: UDP
Кодеки: G.711
Безопасность: Отключена (для тестирования)
```

### **Безопасная конфигурация:**
```
Сервер: sip.yourprovider.com
Порт: 5061
Протокол: TLS
Кодеки: G.711, G.722
TLS: Включен
SRTP: Включен
```

### **Корпоративная конфигурация:**
```
Сервер: sip.company.com
Порт: 5061
Протокол: TLS
Кодеки: G.711, G.722, Opus
TLS: Включен
SRTP: Включен
ICE: Включен
STUN: stun.company.com
Переадресация: Включена
Голосовая почта: Включена
IVR: Включена
```

## 📱 **Настройка SIP-телефонов**

### **Популярные SIP-клиенты:**

#### **Мобильные приложения:**
- **Zoiper** (iOS/Android)
- **Linphone** (iOS/Android)
- **Grandstream Wave** (iOS/Android)

#### **Настольные приложения:**
- **Zoiper** (Windows/Mac/Linux)
- **Jitsi** (Windows/Mac/Linux)
- **MicroSIP** (Windows)

#### **Аппаратные телефоны:**
- **Yealink** (T46S, T48S)
- **Grandstream** (GXP2170, GXP2160)
- **Cisco** (SPA series)

### **Пример настройки в Zoiper:**
```
Account Name: Мой SIP аккаунт
Domain: sip.example.com
Username: 1001
Password: [сгенерированный пароль]
Outbound Proxy: sip.example.com:5060
```

## 🔍 **Диагностика проблем**

### **Проблемы регистрации:**
1. **Неверные учетные данные** - проверьте username/password
2. **Неправильный сервер** - убедитесь в корректности адреса
3. **Блокировка портов** - проверьте firewall
4. **NAT проблемы** - настройте STUN сервер

### **Проблемы с аудио:**
1. **Нет звука** - проверьте кодеки
2. **Плохое качество** - используйте G.722 вместо G.729
3. **Односторонний звук** - настройте STUN/ICE
4. **Эхо** - проверьте настройки эхоподавления

### **Проблемы безопасности:**
1. **TLS ошибки** - проверьте сертификаты
2. **SRTP проблемы** - убедитесь в поддержке клиентом
3. **Аутентификация** - проверьте auth_username

## 📊 **Мониторинг в интерфейсе**

### **Статусы регистрации:**
- 🟢 **Онлайн** - успешно зарегистрирован
- 🔴 **Офлайн** - не зарегистрирован
- 🟡 **Истекает** - скоро потребуется перерегистрация

### **Индикаторы безопасности:**
- 🔒 **TLS** - защищенная сигнализация
- 🔐 **SRTP** - защищенный медиа-трафик

### **Активные функции:**
- 📞 **Переадресация** - включена переадресация
- 📧 **Голос. почта** - включена голосовая почта
- 🎵 **IVR** - включено голосовое меню

## ⚡ **Быстрый старт**

### **Создание базового аккаунта:**
1. Нажмите "Создать SIP аккаунт"
2. Выберите клиента
3. Введите username (например: 1001)
4. Нажмите "Генерировать" для пароля
5. Введите отображаемое имя
6. Нажмите "Создать"

### **Настройка безопасного аккаунта:**
1. Перейдите на вкладку "Безопасность"
2. Включите TLS/SSL
3. Включите SRTP
4. Порт автоматически изменится на 5061
5. Сохраните настройки

### **Добавление функций:**
1. Перейдите на вкладку "Функции"
2. Включите нужные опции
3. Для переадресации укажите номер
4. Сохраните настройки

## 🎯 **Лучшие практики**

### **Безопасность:**
- Всегда используйте TLS для корпоративных аккаунтов
- Включайте SRTP для конфиденциальных разговоров
- Используйте сложные пароли (генератор в форме)
- Регулярно меняйте пароли

### **Производительность:**
- Используйте G.711 для локальных сетей
- Используйте G.729 для экономии трафика
- Настройте STUN для работы через NAT
- Ограничивайте количество одновременных звонков

### **Удобство:**
- Настройте голосовую почту для важных аккаунтов
- Используйте переадресацию для мобильности
- Настройте IVR для автоматизации

## 📞 **Поддержка**

При возникновении проблем:
1. Проверьте статус регистрации в таблице
2. Убедитесь в правильности настроек сервера
3. Проверьте сетевое подключение
4. Обратитесь в техническую поддержку

**Контакты поддержки:**
- Email: <EMAIL>
- Телефон: +7 (XXX) XXX-XX-XX
- Telegram: @spaas_support
