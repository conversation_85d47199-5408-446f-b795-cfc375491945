# Быстрый старт SPaaS Platform

## Предварительные требования

- Node.js 18+ 
- Аккаунт в Supabase
- Git

## 1. Настройка Supabase проекта

1. Создайте новый проект в [Supabase](https://supabase.com)
2. Перейдите в Settings → API
3. Скопируйте:
   - Project URL
   - anon public key

## 2. Клонирование и настройка проекта

```bash
# Клонируйте репозиторий (или скопируйте файлы)
git clone <your-repo-url>
cd spaas-platform

# Запустите скрипт настройки
chmod +x scripts/setup.sh
./scripts/setup.sh
```

## 3. Настройка переменных окружения

Отредактируйте файл `frontend/.env`:

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_APP_NAME=SPaaS Platform
VITE_APP_ENVIRONMENT=development
```

## 4. Настройка базы данных

1. Откройте Supabase Studio → SQL Editor
2. Скопируйте и выполните содержимое файла `scripts/migrate.sql`
3. Проверьте, что таблицы созданы успешно

## 5. Создание первого пользователя

1. В Supabase Studio перейдите в Authentication → Users
2. Создайте нового пользователя:
   - Email: <EMAIL>
   - Password: (ваш пароль)
   - Confirm: true

3. В SQL Editor выполните:

```sql
-- Создаем первого Provider пользователя
SELECT create_initial_provider(
    'USER_ID_FROM_AUTH_USERS',  -- замените на реальный ID
    '<EMAIL>',
    'Platform Administrator'
);
```

## 6. Запуск приложения

```bash
cd frontend
npm run dev
```

Откройте http://localhost:3000 и войдите с созданными учетными данными.

## 7. Создание демо данных (опционально)

В SQL Editor выполните:

```sql
SELECT create_demo_data();
```

## Структура проекта

```
spaas-platform/
├── frontend/           # React + Vite приложение
├── database/           # SQL схемы и миграции
├── supabase/          # Supabase конфигурация
├── docs/              # Документация
└── scripts/           # Утилиты
```

## Следующие шаги

1. Изучите архитектурную документацию в `docs/architecture/`
2. Настройте дополнительные RLS политики
3. Добавьте Edge Functions для бизнес-логики
4. Настройте CI/CD для автоматического развертывания

## Устранение проблем

### Ошибка "User profile not found"
- Убедитесь, что пользователь создан в таблице `users`
- Проверьте, что `tenant_id` корректный

### Ошибки RLS
- Проверьте, что RLS политики применены
- Убедитесь, что JWT содержит правильные claims

### Проблемы с подключением
- Проверьте переменные окружения
- Убедитесь, что Supabase проект активен

## Поддержка

Для получения помощи:
1. Проверьте документацию в `docs/`
2. Изучите логи в браузере и Supabase
3. Обратитесь к команде разработки
