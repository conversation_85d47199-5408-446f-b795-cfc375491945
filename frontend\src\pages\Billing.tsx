import React, { useEffect, useState } from 'react'
import { Plus, Search, Filter, FileText, CreditCard, AlertCircle, CheckCircle, Clock, DollarSign } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { demoInvoices, demoPayments, demoStats } from '@/lib/demoData'

interface Invoice {
  invoice_id: string
  tenant_id: string
  client_id: string
  invoice_number: string
  issue_date: string
  due_date: string
  status: string
  subtotal: number
  tax_amount: number
  total_amount: number
  currency: string
  clients?: {
    company_name: string
    contact_person: string
  }
}

interface Payment {
  payment_id: string
  tenant_id: string
  client_id: string
  invoice_id: string | null
  payment_number: string
  payment_date: string
  amount: number
  currency: string
  payment_method: string
  status: string
  reference_number?: string
  clients?: {
    company_name: string
    contact_person: string
  }
}

export const Billing: React.FC = () => {
  const { user } = useAuthStore()
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'invoices' | 'payments'>('invoices')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchBillingData()
  }, [user])

  const fetchBillingData = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setInvoices(demoInvoices as any)
        setPayments(demoPayments as any)
      } else {
        // Здесь будет реальный запрос к Supabase
        setInvoices([])
        setPayments([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.clients?.company_name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = 
      payment.payment_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.clients?.company_name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getInvoiceStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'overdue':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'sent':
        return <Clock className="h-4 w-4 text-blue-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-400" />
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'bank_transfer':
        return <CreditCard className="h-4 w-4 text-blue-600" />
      case 'card':
        return <CreditCard className="h-4 w-4 text-purple-600" />
      default:
        return <DollarSign className="h-4 w-4 text-green-600" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Биллинг</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление инвойсами и платежами
          </p>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Создать инвойс
          </button>
          <button className="btn btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Добавить платеж
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon blue">
            <FileText className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего инвойсов</h3>
            <p>{demoStats.totalInvoices}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <CheckCircle className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Оплачено</h3>
            <p>{demoStats.paidInvoices}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon red">
            <AlertCircle className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Просрочено</h3>
            <p>{demoStats.overdueInvoices}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <DollarSign className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Общая выручка</h3>
            <p>{formatCurrency(demoStats.totalRevenue, 'RUB')}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <Clock className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>К оплате</h3>
            <p>{formatCurrency(demoStats.outstandingAmount, 'RUB')}</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="card-header">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('invoices')}
              className={`pb-2 border-b-2 font-medium text-sm ${
                activeTab === 'invoices'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Инвойсы ({invoices.length})
            </button>
            <button
              onClick={() => setActiveTab('payments')}
              className={`pb-2 border-b-2 font-medium text-sm ${
                activeTab === 'payments'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Платежи ({payments.length})
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder={`Поиск ${activeTab === 'invoices' ? 'инвойсов' : 'платежей'}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все статусы</option>
                {activeTab === 'invoices' ? (
                  <>
                    <option value="draft">Черновик</option>
                    <option value="sent">Отправлен</option>
                    <option value="paid">Оплачен</option>
                    <option value="overdue">Просрочен</option>
                    <option value="cancelled">Отменен</option>
                  </>
                ) : (
                  <>
                    <option value="pending">Ожидает</option>
                    <option value="completed">Завершен</option>
                    <option value="failed">Неудачный</option>
                    <option value="refunded">Возвращен</option>
                  </>
                )}
              </select>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="table-container">
          {activeTab === 'invoices' ? (
            <table className="table">
              <thead>
                <tr>
                  <th>Номер инвойса</th>
                  <th>Клиент</th>
                  <th>Дата выставления</th>
                  <th>Срок оплаты</th>
                  <th>Сумма</th>
                  <th>Статус</th>
                  <th>Действия</th>
                </tr>
              </thead>
              <tbody>
                {filteredInvoices.map((invoice) => (
                  <tr key={invoice.invoice_id}>
                    <td>
                      <div className="font-medium text-gray-900">
                        {invoice.invoice_number}
                      </div>
                    </td>
                    <td>
                      <div>
                        <div className="text-sm text-gray-900">
                          {invoice.clients?.company_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {invoice.clients?.contact_person}
                        </div>
                      </div>
                    </td>
                    <td className="text-sm text-gray-500">
                      {formatDate(invoice.issue_date)}
                    </td>
                    <td className="text-sm text-gray-500">
                      {formatDate(invoice.due_date)}
                    </td>
                    <td className="font-medium">
                      {formatCurrency(invoice.total_amount, invoice.currency)}
                    </td>
                    <td>
                      <div className="flex items-center">
                        {getInvoiceStatusIcon(invoice.status)}
                        <span className={`ml-2 badge ${invoice.status}`}>
                          {invoice.status}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <button className="p-1 text-gray-400 hover:text-blue-600" title="Просмотр">
                          <FileText className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>Номер платежа</th>
                  <th>Клиент</th>
                  <th>Дата платежа</th>
                  <th>Сумма</th>
                  <th>Способ оплаты</th>
                  <th>Статус</th>
                  <th>Действия</th>
                </tr>
              </thead>
              <tbody>
                {filteredPayments.map((payment) => (
                  <tr key={payment.payment_id}>
                    <td>
                      <div className="font-medium text-gray-900">
                        {payment.payment_number}
                      </div>
                      {payment.reference_number && (
                        <div className="text-xs text-gray-500">
                          Ref: {payment.reference_number}
                        </div>
                      )}
                    </td>
                    <td>
                      <div>
                        <div className="text-sm text-gray-900">
                          {payment.clients?.company_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {payment.clients?.contact_person}
                        </div>
                      </div>
                    </td>
                    <td className="text-sm text-gray-500">
                      {formatDate(payment.payment_date)}
                    </td>
                    <td className="font-medium">
                      {formatCurrency(payment.amount, payment.currency)}
                    </td>
                    <td>
                      <div className="flex items-center">
                        {getPaymentMethodIcon(payment.payment_method)}
                        <span className="ml-2 text-sm">
                          {payment.payment_method}
                        </span>
                      </div>
                    </td>
                    <td>
                      <span className={`badge ${payment.status}`}>
                        {payment.status}
                      </span>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <button className="p-1 text-gray-400 hover:text-blue-600" title="Просмотр">
                          <FileText className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}

          {(activeTab === 'invoices' ? filteredInvoices : filteredPayments).length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm || statusFilter !== 'all'
                  ? `${activeTab === 'invoices' ? 'Инвойсы' : 'Платежи'} не найдены по заданным критериям`
                  : `Пока нет ${activeTab === 'invoices' ? 'инвойсов' : 'платежей'}`
                }
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
