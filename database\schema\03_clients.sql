-- Таблица клиентов арендатора
CREATE TABLE IF NOT EXISTS clients (
    client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    client_code VARCHAR(50), -- Внутренний код клиента у арендатора
    company_name VARCHAR(255),
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    tax_id VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
    billing_type VARCHAR(50) DEFAULT 'postpaid' CHECK (billing_type IN ('prepaid', 'postpaid')),
    credit_limit DECIMAL(10,2) DEFAULT 0,
    current_balance DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    time_zone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    notes TEXT,
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Индексы
CREATE INDEX IF NOT EXISTS idx_clients_tenant_id ON clients(tenant_id);
CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status);
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_phone ON clients(phone);
CREATE INDEX IF NOT EXISTS idx_clients_company_name ON clients(company_name);
CREATE INDEX IF NOT EXISTS idx_clients_client_code ON clients(tenant_id, client_code);
CREATE INDEX IF NOT EXISTS idx_clients_created_at ON clients(created_at);

-- Уникальность client_code в рамках арендатора
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_client_code_per_tenant 
    ON clients(tenant_id, client_code) 
    WHERE client_code IS NOT NULL;

-- Триггер для обновления updated_at
CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON clients 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Комментарии
COMMENT ON TABLE clients IS 'Клиенты арендаторов';
COMMENT ON COLUMN clients.client_id IS 'Уникальный идентификатор клиента';
COMMENT ON COLUMN clients.tenant_id IS 'Арендатор, которому принадлежит клиент';
COMMENT ON COLUMN clients.client_code IS 'Внутренний код клиента у арендатора';
COMMENT ON COLUMN clients.billing_type IS 'Тип биллинга: prepaid или postpaid';
COMMENT ON COLUMN clients.credit_limit IS 'Кредитный лимит для клиента';
COMMENT ON COLUMN clients.current_balance IS 'Текущий баланс клиента';
COMMENT ON COLUMN clients.tags IS 'Теги клиента в формате JSON массива';
COMMENT ON COLUMN clients.custom_fields IS 'Пользовательские поля в формате JSON';
