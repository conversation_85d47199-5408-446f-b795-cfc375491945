import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Speaker, Wifi, Shield, TestTube } from 'lucide-react'
import { Modal, ModalFooter } from '@/components/Modal'

interface WebRTCSettingsProps {
  isOpen: boolean
  onClose: () => void
  onSave: (settings: WebRTCConfig) => void
}

interface WebRTCConfig {
  signalwire: {
    project: string
    token: string
    space: string
  }
  audio: {
    inputDevice: string
    outputDevice: string
    echoCancellation: boolean
    noiseSuppression: boolean
    autoGainControl: boolean
  }
  video: {
    inputDevice: string
    resolution: string
    frameRate: number
    enabled: boolean
  }
  network: {
    iceServers: string[]
    bandwidth: number
    codec: string
  }
}

const defaultConfig: WebRTCConfig = {
  signalwire: {
    project: '',
    token: '',
    space: 'example.signalwire.com'
  },
  audio: {
    inputDevice: 'default',
    outputDevice: 'default',
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true
  },
  video: {
    inputDevice: 'default',
    resolution: '720p',
    frameRate: 30,
    enabled: false
  },
  network: {
    iceServers: ['stun:stun.signalwire.com'],
    bandwidth: 1000,
    codec: 'opus'
  }
}

export const WebRTCSettings: React.FC<WebRTCSettingsProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const [config, setConfig] = useState<WebRTCConfig>(defaultConfig)
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([])
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([])
  const [outputDevices, setOutputDevices] = useState<MediaDeviceInfo[]>([])
  const [testing, setTesting] = useState(false)
  const [testResults, setTestResults] = useState<{
    microphone: boolean
    camera: boolean
    connection: boolean
  } | null>(null)

  useEffect(() => {
    if (isOpen) {
      loadDevices()
      loadSavedConfig()
    }
  }, [isOpen])

  const loadDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      
      setAudioDevices(devices.filter(device => device.kind === 'audioinput'))
      setVideoDevices(devices.filter(device => device.kind === 'videoinput'))
      setOutputDevices(devices.filter(device => device.kind === 'audiooutput'))
    } catch (error) {
      console.error('Failed to load devices:', error)
    }
  }

  const loadSavedConfig = () => {
    const saved = localStorage.getItem('webrtc-config')
    if (saved) {
      try {
        setConfig(JSON.parse(saved))
      } catch (error) {
        console.error('Failed to load saved config:', error)
      }
    }
  }

  const handleConfigChange = (section: keyof WebRTCConfig, key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
  }

  const testDevices = async () => {
    setTesting(true)
    setTestResults(null)

    const results = {
      microphone: false,
      camera: false,
      connection: false
    }

    try {
      // Test microphone
      const audioStream = await navigator.mediaDevices.getUserMedia({ 
        audio: { deviceId: config.audio.inputDevice !== 'default' ? config.audio.inputDevice : undefined }
      })
      results.microphone = true
      audioStream.getTracks().forEach(track => track.stop())

      // Test camera if enabled
      if (config.video.enabled) {
        const videoStream = await navigator.mediaDevices.getUserMedia({ 
          video: { deviceId: config.video.inputDevice !== 'default' ? config.video.inputDevice : undefined }
        })
        results.camera = true
        videoStream.getTracks().forEach(track => track.stop())
      } else {
        results.camera = true // Skip if disabled
      }

      // Test connection (simulate)
      await new Promise(resolve => setTimeout(resolve, 1000))
      results.connection = true

    } catch (error) {
      console.error('Device test failed:', error)
    }

    setTestResults(results)
    setTesting(false)
  }

  const handleSave = () => {
    localStorage.setItem('webrtc-config', JSON.stringify(config))
    onSave(config)
    onClose()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Настройки WebRTC"
      size="lg"
    >
      <div className="space-y-6">
        {/* SignalWire Configuration */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 flex items-center">
            <Wifi className="h-4 w-4 mr-2" />
            Подключение SignalWire
          </h3>
          
          <div className="grid grid-cols-1 gap-4">
            <div className="form-group">
              <label className="form-label required">Project ID</label>
              <input
                type="text"
                value={config.signalwire.project}
                onChange={(e) => handleConfigChange('signalwire', 'project', e.target.value)}
                className="form-input"
                placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
              />
            </div>
            
            <div className="form-group">
              <label className="form-label required">Auth Token</label>
              <input
                type="password"
                value={config.signalwire.token}
                onChange={(e) => handleConfigChange('signalwire', 'token', e.target.value)}
                className="form-input"
                placeholder="PTxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
              />
            </div>
            
            <div className="form-group">
              <label className="form-label required">Space URL</label>
              <input
                type="text"
                value={config.signalwire.space}
                onChange={(e) => handleConfigChange('signalwire', 'space', e.target.value)}
                className="form-input"
                placeholder="example.signalwire.com"
              />
            </div>
          </div>
        </div>

        {/* Audio Settings */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 flex items-center">
            <Mic className="h-4 w-4 mr-2" />
            Настройки аудио
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Микрофон</label>
              <select
                value={config.audio.inputDevice}
                onChange={(e) => handleConfigChange('audio', 'inputDevice', e.target.value)}
                className="form-input"
              >
                <option value="default">По умолчанию</option>
                {audioDevices.map(device => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Микрофон ${device.deviceId.slice(0, 8)}`}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label className="form-label">Динамики</label>
              <select
                value={config.audio.outputDevice}
                onChange={(e) => handleConfigChange('audio', 'outputDevice', e.target.value)}
                className="form-input"
              >
                <option value="default">По умолчанию</option>
                {outputDevices.map(device => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Динамики ${device.deviceId.slice(0, 8)}`}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.audio.echoCancellation}
                onChange={(e) => handleConfigChange('audio', 'echoCancellation', e.target.checked)}
                className="form-checkbox"
              />
              <span className="ml-2 text-sm text-gray-700">Подавление эха</span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.audio.noiseSuppression}
                onChange={(e) => handleConfigChange('audio', 'noiseSuppression', e.target.checked)}
                className="form-checkbox"
              />
              <span className="ml-2 text-sm text-gray-700">Подавление шума</span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.audio.autoGainControl}
                onChange={(e) => handleConfigChange('audio', 'autoGainControl', e.target.checked)}
                className="form-checkbox"
              />
              <span className="ml-2 text-sm text-gray-700">Автоматическая регулировка усиления</span>
            </label>
          </div>
        </div>

        {/* Video Settings */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 flex items-center">
            <Camera className="h-4 w-4 mr-2" />
            Настройки видео
          </h3>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.video.enabled}
              onChange={(e) => handleConfigChange('video', 'enabled', e.target.checked)}
              className="form-checkbox"
            />
            <span className="ml-2 text-sm text-gray-700">Включить видеозвонки</span>
          </label>
          
          {config.video.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="form-group">
                <label className="form-label">Камера</label>
                <select
                  value={config.video.inputDevice}
                  onChange={(e) => handleConfigChange('video', 'inputDevice', e.target.value)}
                  className="form-input"
                >
                  <option value="default">По умолчанию</option>
                  {videoDevices.map(device => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `Камера ${device.deviceId.slice(0, 8)}`}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="form-group">
                <label className="form-label">Разрешение</label>
                <select
                  value={config.video.resolution}
                  onChange={(e) => handleConfigChange('video', 'resolution', e.target.value)}
                  className="form-input"
                >
                  <option value="480p">480p (640x480)</option>
                  <option value="720p">720p (1280x720)</option>
                  <option value="1080p">1080p (1920x1080)</option>
                </select>
              </div>
              
              <div className="form-group">
                <label className="form-label">Частота кадров</label>
                <select
                  value={config.video.frameRate}
                  onChange={(e) => handleConfigChange('video', 'frameRate', parseInt(e.target.value))}
                  className="form-input"
                >
                  <option value={15}>15 FPS</option>
                  <option value={24}>24 FPS</option>
                  <option value={30}>30 FPS</option>
                  <option value={60}>60 FPS</option>
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Network Settings */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            Сетевые настройки
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Пропускная способность (kbps)</label>
              <input
                type="number"
                value={config.network.bandwidth}
                onChange={(e) => handleConfigChange('network', 'bandwidth', parseInt(e.target.value))}
                className="form-input"
                min="64"
                max="2000"
              />
            </div>
            
            <div className="form-group">
              <label className="form-label">Аудио кодек</label>
              <select
                value={config.network.codec}
                onChange={(e) => handleConfigChange('network', 'codec', e.target.value)}
                className="form-input"
              >
                <option value="opus">Opus (рекомендуется)</option>
                <option value="pcmu">PCMU (G.711)</option>
                <option value="pcma">PCMA (G.711)</option>
                <option value="g722">G.722</option>
              </select>
            </div>
          </div>
        </div>

        {/* Device Test */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900 flex items-center">
              <TestTube className="h-4 w-4 mr-2" />
              Тестирование устройств
            </h3>
            <button
              onClick={testDevices}
              disabled={testing}
              className="btn btn-secondary"
            >
              {testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Тестирование...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4 mr-2" />
                  Тестировать
                </>
              )}
            </button>
          </div>

          {testResults && (
            <div className="space-y-2">
              <div className={`flex items-center p-2 rounded ${
                testResults.microphone ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
              }`}>
                <Mic className="h-4 w-4 mr-2" />
                Микрофон: {testResults.microphone ? 'Работает' : 'Ошибка'}
              </div>
              
              {config.video.enabled && (
                <div className={`flex items-center p-2 rounded ${
                  testResults.camera ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                }`}>
                  <Camera className="h-4 w-4 mr-2" />
                  Камера: {testResults.camera ? 'Работает' : 'Ошибка'}
                </div>
              )}
              
              <div className={`flex items-center p-2 rounded ${
                testResults.connection ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
              }`}>
                <Wifi className="h-4 w-4 mr-2" />
                Подключение: {testResults.connection ? 'Успешно' : 'Ошибка'}
              </div>
            </div>
          )}
        </div>
      </div>

      <ModalFooter>
        <button
          type="button"
          onClick={onClose}
          className="btn btn-secondary"
        >
          Отмена
        </button>
        <button
          onClick={handleSave}
          className="btn btn-primary"
        >
          <Settings className="h-4 w-4 mr-2" />
          Сохранить
        </button>
      </ModalFooter>
    </Modal>
  )
}
