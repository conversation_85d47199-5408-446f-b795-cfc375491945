import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { <PERSON><PERSON>, ModalFooter } from './Modal'

interface SipAccountFormData {
  client_id: string
  username: string
  password: string
  display_name: string
  max_concurrent_calls: number
  status: string
}

interface SipAccountFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: SipAccountFormData) => Promise<void>
  clients: Array<{ client_id: string; company_name: string }>
  initialData?: Partial<SipAccountFormData>
  mode?: 'create' | 'edit'
}

export const SipAccountForm: React.FC<SipAccountFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  clients,
  initialData,
  mode = 'create'
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<SipAccountFormData>({
    defaultValues: {
      client_id: initialData?.client_id || '',
      username: initialData?.username || '',
      password: '',
      display_name: initialData?.display_name || '',
      max_concurrent_calls: initialData?.max_concurrent_calls || 1,
      status: initialData?.status || 'active'
    }
  })

  const handleFormSubmit = async (data: SipAccountFormData) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data)
      reset()
      onClose()
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  // Генерация случайного пароля
  const generatePassword = () => {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Создать SIP аккаунт' : 'Редактировать SIP аккаунт'}
      size="md"
    >
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* Клиент */}
        <div className="form-group">
          <label className="form-label">
            Клиент *
          </label>
          <select
            {...register('client_id', { required: 'Выберите клиента' })}
            className="form-input"
            disabled={mode === 'edit'}
          >
            <option value="">Выберите клиента</option>
            {clients.map((client) => (
              <option key={client.client_id} value={client.client_id}>
                {client.company_name}
              </option>
            ))}
          </select>
          {errors.client_id && (
            <p className="error-message">{errors.client_id.message}</p>
          )}
        </div>

        {/* Username */}
        <div className="form-group">
          <label className="form-label">
            SIP Username *
          </label>
          <input
            type="text"
            {...register('username', { 
              required: 'Введите SIP username',
              pattern: {
                value: /^[a-zA-Z0-9_-]+$/,
                message: 'Только буквы, цифры, дефис и подчеркивание'
              }
            })}
            className="form-input"
            placeholder="1001"
          />
          {errors.username && (
            <p className="error-message">{errors.username.message}</p>
          )}
        </div>

        {/* Password */}
        <div className="form-group">
          <label className="form-label">
            Пароль *
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              {...register('password', { 
                required: mode === 'create' ? 'Введите пароль' : false,
                minLength: {
                  value: 8,
                  message: 'Минимум 8 символов'
                }
              })}
              className="form-input flex-1"
              placeholder={mode === 'edit' ? 'Оставьте пустым для сохранения текущего' : 'Введите пароль'}
            />
            <button
              type="button"
              onClick={() => {
                const password = generatePassword()
                // Устанавливаем значение в форму
                const passwordInput = document.querySelector('input[name="password"]') as HTMLInputElement
                if (passwordInput) {
                  passwordInput.value = password
                }
              }}
              className="btn btn-primary px-3"
            >
              Генерировать
            </button>
          </div>
          {errors.password && (
            <p className="error-message">{errors.password.message}</p>
          )}
        </div>

        {/* Display Name */}
        <div className="form-group">
          <label className="form-label">
            Отображаемое имя *
          </label>
          <input
            type="text"
            {...register('display_name', { required: 'Введите отображаемое имя' })}
            className="form-input"
            placeholder="Иван Петров"
          />
          {errors.display_name && (
            <p className="error-message">{errors.display_name.message}</p>
          )}
        </div>

        {/* Max Concurrent Calls */}
        <div className="form-group">
          <label className="form-label">
            Максимум одновременных звонков
          </label>
          <input
            type="number"
            {...register('max_concurrent_calls', { 
              required: 'Введите количество',
              min: { value: 1, message: 'Минимум 1' },
              max: { value: 10, message: 'Максимум 10' }
            })}
            className="form-input"
            min="1"
            max="10"
          />
          {errors.max_concurrent_calls && (
            <p className="error-message">{errors.max_concurrent_calls.message}</p>
          )}
        </div>

        {/* Status */}
        <div className="form-group">
          <label className="form-label">
            Статус
          </label>
          <select
            {...register('status')}
            className="form-input"
          >
            <option value="active">Активный</option>
            <option value="inactive">Неактивный</option>
            <option value="suspended">Приостановлен</option>
          </select>
        </div>

        <ModalFooter>
          <button
            type="button"
            onClick={handleClose}
            className="btn"
            style={{ backgroundColor: '#f3f4f6', color: '#374151' }}
          >
            Отмена
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn btn-primary"
          >
            {isSubmitting ? 'Сохранение...' : (mode === 'create' ? 'Создать' : 'Сохранить')}
          </button>
        </ModalFooter>
      </form>
    </Modal>
  )
}
