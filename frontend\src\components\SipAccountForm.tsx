import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { <PERSON><PERSON>, Mo<PERSON>Footer } from './Modal'

interface SipAccountFormData {
  client_id: string
  username: string
  password: string
  display_name: string
  max_concurrent_calls: number
  status: string
  // Настройки сервера
  sip_server: string
  sip_port: number
  protocol: string
  // Настройки безопасности
  enable_tls: boolean
  enable_srtp: boolean
  auth_username: string
  // Настройки NAT/STUN
  stun_server: string
  enable_ice: boolean
  // Кодеки
  audio_codecs: string[]
  video_codecs: string[]
  // Дополнительные настройки
  call_forwarding_enabled: boolean
  call_forwarding_number: string
  voicemail_enabled: boolean
  ivr_enabled: boolean
}

interface SipAccountFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: SipAccountFormData) => Promise<void>
  clients: Array<{ client_id: string; company_name: string }>
  initialData?: Partial<SipAccountFormData>
  mode?: 'create' | 'edit'
}

export const SipAccountForm: React.FC<SipAccountFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  clients,
  initialData,
  mode = 'create'
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<SipAccountFormData>({
    defaultValues: {
      client_id: initialData?.client_id || '',
      username: initialData?.username || '',
      password: '',
      display_name: initialData?.display_name || '',
      max_concurrent_calls: initialData?.max_concurrent_calls || 1,
      status: initialData?.status || 'active',
      // Настройки сервера
      sip_server: initialData?.sip_server || 'sip.demo.com',
      sip_port: initialData?.sip_port || 5060,
      protocol: initialData?.protocol || 'UDP',
      // Настройки безопасности
      enable_tls: initialData?.enable_tls || false,
      enable_srtp: initialData?.enable_srtp || false,
      auth_username: initialData?.auth_username || '',
      // Настройки NAT/STUN
      stun_server: initialData?.stun_server || 'stun.demo.com',
      enable_ice: initialData?.enable_ice || true,
      // Кодеки
      audio_codecs: initialData?.audio_codecs || ['G.711'],
      video_codecs: initialData?.video_codecs || [],
      // Дополнительные настройки
      call_forwarding_enabled: initialData?.call_forwarding_enabled || false,
      call_forwarding_number: initialData?.call_forwarding_number || '',
      voicemail_enabled: initialData?.voicemail_enabled || true,
      ivr_enabled: initialData?.ivr_enabled || false
    }
  })

  const handleFormSubmit = async (data: SipAccountFormData) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data)
      reset()
      onClose()
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  // Генерация случайного пароля
  const generatePassword = () => {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  const [activeTab, setActiveTab] = useState<'basic' | 'server' | 'security' | 'codecs' | 'features'>('basic')

  const availableAudioCodecs = ['G.711', 'G.722', 'G.729', 'Opus', 'GSM']
  const availableVideoCodecs = ['H.264', 'VP8', 'VP9', 'H.265']

  const watchedValues = watch()

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Создать SIP аккаунт' : 'Редактировать SIP аккаунт'}
      size="xl"
    >
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'basic', name: 'Основные', icon: '👤' },
              { id: 'server', name: 'Сервер', icon: '🖥️' },
              { id: 'security', name: 'Безопасность', icon: '🔒' },
              { id: 'codecs', name: 'Кодеки', icon: '🎵' },
              { id: 'features', name: 'Функции', icon: '⚙️' }
            ].map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
        {/* Tab Content */}
        <div className="mt-6">
          {/* Основные настройки */}
          {activeTab === 'basic' && (
            <div className="space-y-4">
              {/* Клиент */}
              <div className="form-group">
                <label className="form-label">
                  Клиент *
                </label>
                <select
                  {...register('client_id', { required: 'Выберите клиента' })}
                  className="form-input"
                  disabled={mode === 'edit'}
                >
                  <option value="">Выберите клиента</option>
                  {clients.map((client) => (
                    <option key={client.client_id} value={client.client_id}>
                      {client.company_name}
                    </option>
                  ))}
                </select>
                {errors.client_id && (
                  <p className="error-message">{errors.client_id.message}</p>
                )}
              </div>

              {/* Username */}
              <div className="form-group">
                <label className="form-label">
                  SIP Username *
                </label>
                <input
                  type="text"
                  {...register('username', {
                    required: 'Введите SIP username',
                    pattern: {
                      value: /^[a-zA-Z0-9_-]+$/,
                      message: 'Только буквы, цифры, дефис и подчеркивание'
                    }
                  })}
                  className="form-input"
                  placeholder="1001"
                  onChange={(e) => {
                    // Автоматически заполняем auth_username
                    setValue('auth_username', e.target.value)
                  }}
                />
                {errors.username && (
                  <p className="error-message">{errors.username.message}</p>
                )}
              </div>

              {/* Password */}
              <div className="form-group">
                <label className="form-label">
                  Пароль *
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    {...register('password', {
                      required: mode === 'create' ? 'Введите пароль' : false,
                      minLength: {
                        value: 8,
                        message: 'Минимум 8 символов'
                      }
                    })}
                    className="form-input flex-1"
                    placeholder={mode === 'edit' ? 'Оставьте пустым для сохранения текущего' : 'Введите пароль'}
                  />
                  <button
                    type="button"
                    onClick={() => {
                      const password = generatePassword()
                      setValue('password', password)
                    }}
                    className="btn btn-primary px-3"
                  >
                    Генерировать
                  </button>
                </div>
                {errors.password && (
                  <p className="error-message">{errors.password.message}</p>
                )}
              </div>

              {/* Display Name */}
              <div className="form-group">
                <label className="form-label">
                  Отображаемое имя *
                </label>
                <input
                  type="text"
                  {...register('display_name', { required: 'Введите отображаемое имя' })}
                  className="form-input"
                  placeholder="Иван Петров"
                />
                {errors.display_name && (
                  <p className="error-message">{errors.display_name.message}</p>
                )}
              </div>

              {/* Max Concurrent Calls */}
              <div className="form-group">
                <label className="form-label">
                  Максимум одновременных звонков
                </label>
                <input
                  type="number"
                  {...register('max_concurrent_calls', {
                    required: 'Введите количество',
                    min: { value: 1, message: 'Минимум 1' },
                    max: { value: 10, message: 'Максимум 10' }
                  })}
                  className="form-input"
                  min="1"
                  max="10"
                />
                {errors.max_concurrent_calls && (
                  <p className="error-message">{errors.max_concurrent_calls.message}</p>
                )}
              </div>

              {/* Status */}
              <div className="form-group">
                <label className="form-label">
                  Статус
                </label>
                <select
                  {...register('status')}
                  className="form-input"
                >
                  <option value="active">Активный</option>
                  <option value="inactive">Неактивный</option>
                  <option value="suspended">Приостановлен</option>
                </select>
              </div>
            </div>
          )}

          {/* Настройки сервера */}
          {activeTab === 'server' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* SIP Server */}
                <div className="form-group">
                  <label className="form-label">
                    SIP Сервер *
                  </label>
                  <input
                    type="text"
                    {...register('sip_server', { required: 'Введите адрес SIP сервера' })}
                    className="form-input"
                    placeholder="sip.example.com"
                  />
                  {errors.sip_server && (
                    <p className="error-message">{errors.sip_server.message}</p>
                  )}
                </div>

                {/* SIP Port */}
                <div className="form-group">
                  <label className="form-label">
                    Порт SIP
                  </label>
                  <input
                    type="number"
                    {...register('sip_port', {
                      required: 'Введите порт',
                      min: { value: 1, message: 'Минимум 1' },
                      max: { value: 65535, message: 'Максимум 65535' }
                    })}
                    className="form-input"
                    placeholder="5060"
                  />
                  {errors.sip_port && (
                    <p className="error-message">{errors.sip_port.message}</p>
                  )}
                </div>
              </div>

              {/* Protocol */}
              <div className="form-group">
                <label className="form-label">
                  Протокол
                </label>
                <select
                  {...register('protocol')}
                  className="form-input"
                >
                  <option value="UDP">UDP</option>
                  <option value="TCP">TCP</option>
                  <option value="TLS">TLS</option>
                </select>
              </div>

              {/* STUN Server */}
              <div className="form-group">
                <label className="form-label">
                  STUN Сервер
                </label>
                <input
                  type="text"
                  {...register('stun_server')}
                  className="form-input"
                  placeholder="stun.example.com"
                />
              </div>

              {/* Enable ICE */}
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('enable_ice')}
                    className="mr-2"
                  />
                  Включить ICE (Interactive Connectivity Establishment)
                </label>
              </div>
            </div>
          )}

          {/* Настройки безопасности */}
          {activeTab === 'security' && (
            <div className="space-y-4">
              {/* Enable TLS */}
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('enable_tls')}
                    className="mr-2"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setValue('sip_port', 5061)
                        setValue('protocol', 'TLS')
                      } else {
                        setValue('sip_port', 5060)
                        setValue('protocol', 'UDP')
                      }
                    }}
                  />
                  Включить TLS/SSL шифрование
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Защищенное соединение для SIP сигнализации
                </p>
              </div>

              {/* Enable SRTP */}
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('enable_srtp')}
                    className="mr-2"
                  />
                  Включить SRTP шифрование
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Защищенная передача аудио/видео трафика
                </p>
              </div>

              {/* Auth Username */}
              <div className="form-group">
                <label className="form-label">
                  Имя для аутентификации
                </label>
                <input
                  type="text"
                  {...register('auth_username')}
                  className="form-input"
                  placeholder="Обычно совпадает с SIP username"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Если отличается от SIP username
                </p>
              </div>
            </div>
          )}

          {/* Настройки кодеков */}
          {activeTab === 'codecs' && (
            <div className="space-y-4">
              {/* Audio Codecs */}
              <div className="form-group">
                <label className="form-label">
                  Аудио кодеки
                </label>
                <div className="space-y-2">
                  {availableAudioCodecs.map((codec) => (
                    <label key={codec} className="flex items-center">
                      <input
                        type="checkbox"
                        value={codec}
                        {...register('audio_codecs')}
                        className="mr-2"
                      />
                      {codec}
                      {codec === 'G.711' && <span className="text-xs text-gray-500 ml-2">(Рекомендуется)</span>}
                    </label>
                  ))}
                </div>
              </div>

              {/* Video Codecs */}
              <div className="form-group">
                <label className="form-label">
                  Видео кодеки
                </label>
                <div className="space-y-2">
                  {availableVideoCodecs.map((codec) => (
                    <label key={codec} className="flex items-center">
                      <input
                        type="checkbox"
                        value={codec}
                        {...register('video_codecs')}
                        className="mr-2"
                      />
                      {codec}
                      {codec === 'H.264' && <span className="text-xs text-gray-500 ml-2">(Рекомендуется)</span>}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Дополнительные функции */}
          {activeTab === 'features' && (
            <div className="space-y-4">
              {/* Call Forwarding */}
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('call_forwarding_enabled')}
                    className="mr-2"
                  />
                  Включить переадресацию звонков
                </label>

                {watchedValues.call_forwarding_enabled && (
                  <div className="mt-2">
                    <input
                      type="text"
                      {...register('call_forwarding_number')}
                      className="form-input"
                      placeholder="+7 (XXX) XXX-XX-XX"
                    />
                  </div>
                )}
              </div>

              {/* Voicemail */}
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('voicemail_enabled')}
                    className="mr-2"
                  />
                  Включить голосовую почту
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Автоматическая запись сообщений при недоступности
                </p>
              </div>

              {/* IVR */}
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('ivr_enabled')}
                    className="mr-2"
                  />
                  Включить IVR (Интерактивное голосовое меню)
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Автоматическое меню для входящих звонков
                </p>
              </div>
            </div>
          )}
        </div>

        <ModalFooter>
          <button
            type="button"
            onClick={handleClose}
            className="btn"
            style={{ backgroundColor: '#f3f4f6', color: '#374151' }}
          >
            Отмена
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn btn-primary"
          >
            {isSubmitting ? 'Сохранение...' : (mode === 'create' ? 'Создать' : 'Сохранить')}
          </button>
        </ModalFooter>
      </form>
    </Modal>
  )
}
