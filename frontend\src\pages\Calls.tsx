import React, { useState, useEffect, useRef } from 'react'
import {
  PhoneCall,
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Settings,
  Users,
  Clock,
  History,
  Search,
  Plus,
  Video,
  VideoOff
} from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate } from '@/lib/utils'
import { WebRTCSettings } from '@/components/WebRTCSettings'
import { VideoPlayer } from '@/components/VideoPlayer'
import { AudioPlayer } from '@/components/AudioPlayer'

interface CallHistory {
  call_id: string
  direction: 'inbound' | 'outbound'
  from: string
  to: string
  duration: number
  status: 'completed' | 'missed' | 'busy' | 'failed'
  started_at: string
  ended_at?: string
}

interface Contact {
  contact_id: string
  name: string
  phone: string
  company?: string
  type: 'client' | 'internal' | 'external'
}

// Демо данные истории звонков
const demoCallHistory: CallHistory[] = [
  {
    call_id: 'call-001',
    direction: 'outbound',
    from: '+1234567890',
    to: '+1987654321',
    duration: 245,
    status: 'completed',
    started_at: '2024-12-01T14:30:00Z',
    ended_at: '2024-12-01T14:34:05Z'
  },
  {
    call_id: 'call-002',
    direction: 'inbound',
    from: '+1555666777',
    to: '+1234567890',
    duration: 0,
    status: 'missed',
    started_at: '2024-12-01T13:15:00Z'
  },
  {
    call_id: 'call-003',
    direction: 'outbound',
    from: '+1234567890',
    to: '+1111222333',
    duration: 128,
    status: 'completed',
    started_at: '2024-12-01T12:45:00Z',
    ended_at: '2024-12-01T12:47:08Z'
  },
  {
    call_id: 'call-004',
    direction: 'inbound',
    from: '+1444555666',
    to: '+1234567890',
    duration: 67,
    status: 'completed',
    started_at: '2024-12-01T11:20:00Z',
    ended_at: '2024-12-01T11:21:07Z'
  },
  {
    call_id: 'call-005',
    direction: 'outbound',
    from: '+1234567890',
    to: '+1777888999',
    duration: 0,
    status: 'busy',
    started_at: '2024-12-01T10:30:00Z'
  }
]

// Демо контакты
const demoContacts: Contact[] = [
  {
    contact_id: 'contact-001',
    name: 'Иван Петров',
    phone: '+1987654321',
    company: 'ООО "Демо Компания"',
    type: 'client'
  },
  {
    contact_id: 'contact-002',
    name: 'Мария Сидорова',
    phone: '+1555666777',
    company: 'ЗАО "Технологии Связи"',
    type: 'client'
  },
  {
    contact_id: 'contact-003',
    name: 'Техподдержка',
    phone: '+1111222333',
    type: 'internal'
  },
  {
    contact_id: 'contact-004',
    name: 'Алексей Козлов',
    phone: '+1444555666',
    company: 'ИП Козлов А.В.',
    type: 'client'
  }
]

export const Calls: React.FC = () => {
  const { user } = useAuthStore()
  const [isConnected, setIsConnected] = useState(false)
  const [isInCall, setIsInCall] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isSpeakerOn, setIsSpeakerOn] = useState(false)
  const [isVideoOn, setIsVideoOn] = useState(false)
  const [callDuration, setCallDuration] = useState(0)
  const [phoneNumber, setPhoneNumber] = useState('')
  const [callHistory, setCallHistory] = useState<CallHistory[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [activeTab, setActiveTab] = useState<'dialer' | 'history' | 'contacts'>('dialer')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentCall, setCurrentCall] = useState<{
    direction: 'inbound' | 'outbound'
    number: string
    name?: string
  } | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [localStream, setLocalStream] = useState<MediaStream | null>(null)
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null)
  const [isRemoteAudioEnabled, setIsRemoteAudioEnabled] = useState(true)

  const callTimerRef = useRef<NodeJS.Timeout>()
  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    fetchCallData()
    initializeWebRTC()

    return () => {
      if (callTimerRef.current) {
        clearInterval(callTimerRef.current)
      }
    }
  }, [user])

  const fetchCallData = async () => {
    if (!user) return

    try {
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setCallHistory(demoCallHistory)
        setContacts(demoContacts)
      } else {
        // Здесь будет реальный запрос к Supabase
        setCallHistory([])
        setContacts([])
      }
    } catch (error) {
      console.error('Error:', error)
    }
  }

  const initializeWebRTC = async () => {
    try {
      // Инициализация SignalWire WebRTC SDK
      // В реальном приложении здесь будет подключение к SignalWire
      console.log('Initializing SignalWire WebRTC...')
      
      // Симуляция подключения
      setTimeout(() => {
        setIsConnected(true)
      }, 1000)
    } catch (error) {
      console.error('WebRTC initialization failed:', error)
    }
  }

  const startCall = async (number: string) => {
    if (!isConnected) {
      alert('Не подключен к серверу звонков')
      return
    }

    try {
      // Получение локального медиа-потока
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: isVideoOn
      })
      setLocalStream(stream)

      // Поиск контакта
      const contact = contacts.find(c => c.phone === number)

      setCurrentCall({
        direction: 'outbound',
        number,
        name: contact?.name
      })

      setIsInCall(true)
      setCallDuration(0)

      // Запуск таймера
      callTimerRef.current = setInterval(() => {
        setCallDuration(prev => prev + 1)
      }, 1000)

      // В реальном приложении здесь будет вызов SignalWire API
      console.log(`Starting call to ${number}`)

      // Симуляция удаленного потока через 3 секунды
      setTimeout(() => {
        simulateRemoteStream()
      }, 3000)

      // Симуляция входящего звонка через 10 секунд
      setTimeout(() => {
        simulateIncomingCall()
      }, 10000)

    } catch (error) {
      console.error('Failed to start call:', error)
      alert('Не удалось получить доступ к камере/микрофону')
      endCall()
    }
  }

  const simulateRemoteStream = async () => {
    try {
      // В реальном приложении здесь будет удаленный поток от SignalWire
      // Для демо создаем фиктивный поток
      const canvas = document.createElement('canvas')
      canvas.width = 640
      canvas.height = 480
      const ctx = canvas.getContext('2d')

      // Создаем анимированный фон
      const animate = () => {
        if (!ctx) return

        const time = Date.now() * 0.001
        ctx.fillStyle = `hsl(${(time * 50) % 360}, 50%, 30%)`
        ctx.fillRect(0, 0, canvas.width, canvas.height)

        // Добавляем текст
        ctx.fillStyle = 'white'
        ctx.font = '24px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('Удаленное видео', canvas.width / 2, canvas.height / 2)
        ctx.fillText('(Демо)', canvas.width / 2, canvas.height / 2 + 30)

        requestAnimationFrame(animate)
      }
      animate()

      const stream = canvas.captureStream(30)
      setRemoteStream(stream)
    } catch (error) {
      console.error('Failed to create remote stream:', error)
    }
  }

  const endCall = () => {
    if (callTimerRef.current) {
      clearInterval(callTimerRef.current)
    }

    // Остановка медиа-потоков
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop())
      setLocalStream(null)
    }
    if (remoteStream) {
      remoteStream.getTracks().forEach(track => track.stop())
      setRemoteStream(null)
    }

    // Добавляем звонок в историю
    if (currentCall) {
      const newCall: CallHistory = {
        call_id: `call-${Date.now()}`,
        direction: currentCall.direction,
        from: currentCall.direction === 'outbound' ? '+1234567890' : currentCall.number,
        to: currentCall.direction === 'outbound' ? currentCall.number : '+1234567890',
        duration: callDuration,
        status: callDuration > 0 ? 'completed' : 'missed',
        started_at: new Date(Date.now() - callDuration * 1000).toISOString(),
        ended_at: new Date().toISOString()
      }

      setCallHistory(prev => [newCall, ...prev])
    }

    setIsInCall(false)
    setCurrentCall(null)
    setCallDuration(0)
    setIsMuted(false)
    setIsSpeakerOn(false)
    setIsVideoOn(false)
    setIsRemoteAudioEnabled(true)
  }

  const simulateIncomingCall = () => {
    if (isInCall) return

    const randomContact = contacts[Math.floor(Math.random() * contacts.length)]
    
    setCurrentCall({
      direction: 'inbound',
      number: randomContact.phone,
      name: randomContact.name
    })
    
    setIsInCall(true)
    setCallDuration(0)
    
    // Автоматически принимаем звонок через 3 секунды для демо
    setTimeout(() => {
      if (isInCall) {
        callTimerRef.current = setInterval(() => {
          setCallDuration(prev => prev + 1)
        }, 1000)
      }
    }, 3000)
  }

  const toggleMute = () => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = isMuted
        setIsMuted(!isMuted)
      }
    }
  }

  const toggleSpeaker = () => {
    setIsSpeakerOn(!isSpeakerOn)
    // В реальном приложении здесь будет управление динамиком
  }

  const toggleVideo = async () => {
    if (isVideoOn) {
      // Выключаем видео
      if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0]
        if (videoTrack) {
          videoTrack.stop()
          localStream.removeTrack(videoTrack)
        }
      }
      setIsVideoOn(false)
    } else {
      // Включаем видео
      try {
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true })
        const videoTrack = videoStream.getVideoTracks()[0]

        if (localStream && videoTrack) {
          localStream.addTrack(videoTrack)
          setIsVideoOn(true)
        }
      } catch (error) {
        console.error('Failed to enable video:', error)
        alert('Не удалось включить видео')
      }
    }
  }

  const toggleRemoteAudio = () => {
    setIsRemoteAudioEnabled(!isRemoteAudioEnabled)
    // В реальном приложении здесь будет управление звуком удаленного участника
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleSaveSettings = (settings: any) => {
    console.log('WebRTC settings saved:', settings)
    // В реальном приложении здесь будет переинициализация WebRTC с новыми настройками
    initializeWebRTC()
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Phone className="h-4 w-4 text-green-600" />
      case 'missed':
        return <PhoneOff className="h-4 w-4 text-red-600" />
      case 'busy':
        return <PhoneOff className="h-4 w-4 text-yellow-600" />
      case 'failed':
        return <PhoneOff className="h-4 w-4 text-red-600" />
      default:
        return <Phone className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: 'Завершен', className: 'badge-success' },
      missed: { label: 'Пропущен', className: 'badge-danger' },
      busy: { label: 'Занято', className: 'badge-warning' },
      failed: { label: 'Ошибка', className: 'badge-danger' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.completed
    
    return (
      <span className={`badge ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const filteredHistory = callHistory.filter(call => 
    call.from.includes(searchTerm) || 
    call.to.includes(searchTerm) ||
    contacts.find(c => c.phone === call.from || c.phone === call.to)?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.phone.includes(searchTerm) ||
    contact.company?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Звонки</h1>
          <p className="mt-1 text-sm text-gray-500">
            WebRTC звонки через SignalWire
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
            isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>{isConnected ? 'Подключен' : 'Отключен'}</span>
          </div>
          <button
            onClick={() => setShowSettings(true)}
            className="btn btn-secondary"
          >
            <Settings className="h-4 w-4 mr-2" />
            Настройки
          </button>
        </div>
      </div>

      {/* Active Call */}
      {isInCall && currentCall && (
        <div className="card p-0 overflow-hidden">
          {isVideoOn || remoteStream ? (
            /* Video Call Interface */
            <VideoPlayer
              localStream={localStream}
              remoteStream={remoteStream}
              isLocalMuted={isMuted}
              isRemoteAudioEnabled={isRemoteAudioEnabled}
              isLocalVideoEnabled={isVideoOn}
              isRemoteVideoEnabled={true}
              participantName={currentCall.name}
              participantNumber={currentCall.number}
              callDuration={callDuration}
              onToggleLocalMute={toggleMute}
              onToggleLocalVideo={toggleVideo}
              onToggleRemoteAudio={toggleRemoteAudio}
              onEndCall={endCall}
              className="h-[600px]"
            />
          ) : (
            /* Audio Call Interface */
            <AudioPlayer
              localStream={localStream}
              remoteStream={remoteStream}
              isLocalMuted={isMuted}
              isRemoteAudioEnabled={isRemoteAudioEnabled}
              participantName={currentCall.name}
              participantNumber={currentCall.number}
              callDuration={callDuration}
              callDirection={currentCall.direction}
              onToggleLocalMute={toggleMute}
              onToggleRemoteAudio={toggleRemoteAudio}
              onEndCall={endCall}
              className="min-h-[500px]"
            />
          )}
        </div>
      )}

      {/* Main Interface */}
      {!isInCall && (
        <div className="card">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('dialer')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'dialer'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <PhoneCall className="h-4 w-4 inline mr-2" />
                Набор номера
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <History className="h-4 w-4 inline mr-2" />
                История ({callHistory.length})
              </button>
              <button
                onClick={() => setActiveTab('contacts')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'contacts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Users className="h-4 w-4 inline mr-2" />
                Контакты ({contacts.length})
              </button>
            </nav>
          </div>

          <div className="p-6">
            {/* Dialer Tab */}
            {activeTab === 'dialer' && (
              <div className="max-w-md mx-auto">
                <div className="mb-6">
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Введите номер телефона"
                    className="form-input text-center text-lg"
                  />
                </div>

                {/* Dialer Pad */}
                <div className="grid grid-cols-3 gap-3 mb-6">
                  {['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'].map((digit) => (
                    <button
                      key={digit}
                      onClick={() => setPhoneNumber(prev => prev + digit)}
                      className="h-12 w-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-lg font-medium"
                    >
                      {digit}
                    </button>
                  ))}
                </div>

                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => setPhoneNumber('')}
                    className="btn btn-secondary"
                    disabled={!phoneNumber}
                  >
                    Очистить
                  </button>
                  <button
                    onClick={() => startCall(phoneNumber)}
                    className="btn btn-primary"
                    disabled={!phoneNumber || !isConnected}
                  >
                    <PhoneCall className="h-4 w-4 mr-2" />
                    Позвонить
                  </button>
                </div>
              </div>
            )}

            {/* History Tab */}
            {activeTab === 'history' && (
              <div>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Поиск в истории..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="form-input pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  {filteredHistory.map((call) => {
                    const contact = contacts.find(c => c.phone === call.from || c.phone === call.to)
                    const displayNumber = call.direction === 'outbound' ? call.to : call.from
                    
                    return (
                      <div key={call.call_id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(call.status)}
                          <div>
                            <div className="font-medium text-gray-900">
                              {contact?.name || displayNumber}
                            </div>
                            <div className="text-sm text-gray-500">
                              {contact?.name && displayNumber} • {call.direction === 'outbound' ? 'Исходящий' : 'Входящий'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-900">
                            {formatDate(call.started_at, 'time')}
                          </div>
                          <div className="text-xs text-gray-500">
                            {call.duration > 0 ? formatDuration(call.duration) : getStatusBadge(call.status)}
                          </div>
                        </div>
                      </div>
                    )
                  })}

                  {filteredHistory.length === 0 && (
                    <div className="text-center py-8">
                      <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <div className="text-gray-500">
                        {searchTerm ? 'Звонки не найдены' : 'История звонков пуста'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Contacts Tab */}
            {activeTab === 'contacts' && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <div className="relative flex-1 mr-4">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Поиск контактов..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="form-input pl-10"
                    />
                  </div>
                  <button className="btn btn-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Добавить
                  </button>
                </div>

                <div className="space-y-3">
                  {filteredContacts.map((contact) => (
                    <div key={contact.contact_id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                          {contact.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{contact.name}</div>
                          <div className="text-sm text-gray-500">
                            {contact.phone}
                            {contact.company && ` • ${contact.company}`}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => startCall(contact.phone)}
                        className="btn btn-primary text-sm"
                        disabled={!isConnected}
                      >
                        <PhoneCall className="h-3 w-3 mr-1" />
                        Позвонить
                      </button>
                    </div>
                  ))}

                  {filteredContacts.length === 0 && (
                    <div className="text-center py-8">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <div className="text-gray-500">
                        {searchTerm ? 'Контакты не найдены' : 'Список контактов пуст'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* WebRTC Settings Modal */}
      <WebRTCSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSave={handleSaveSettings}
      />
    </div>
  )
}
