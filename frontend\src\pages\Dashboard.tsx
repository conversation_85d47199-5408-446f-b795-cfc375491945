import React, { useEffect, useState } from 'react'
import { 
  Users, 
  Phone, 
  Hash, 
  Ticket, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Activity
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/store/authStore'
import { formatCurrency } from '@/lib/utils'

interface DashboardStats {
  totalClients: number
  activeSipAccounts: number
  assignedDidNumbers: number
  openTickets: number
  monthlyRevenue: number
  clientsGrowth: number
  ticketsResolved: number
  systemUptime: number
}

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    isPositive: boolean
  }
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple'
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  color = 'blue' 
}) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
    purple: 'bg-purple-500'
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`p-3 rounded-md ${colorClasses[color]}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {value}
                </div>
                {trend && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                    trend.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {trend.isPositive ? (
                      <TrendingUp className="self-center flex-shrink-0 h-4 w-4" />
                    ) : (
                      <TrendingDown className="self-center flex-shrink-0 h-4 w-4" />
                    )}
                    <span className="ml-1">
                      {Math.abs(trend.value)}%
                    </span>
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  )
}

export const Dashboard: React.FC = () => {
  const { user } = useAuthStore()
  const [stats, setStats] = useState<DashboardStats>({
    totalClients: 0,
    activeSipAccounts: 0,
    assignedDidNumbers: 0,
    openTickets: 0,
    monthlyRevenue: 0,
    clientsGrowth: 0,
    ticketsResolved: 0,
    systemUptime: 99.9
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [user])

  const fetchDashboardStats = async () => {
    if (!user) return

    try {
      setLoading(true)

      // Получаем статистику клиентов
      const { count: clientsCount } = await supabase
        .from('clients')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active')

      // Получаем статистику SIP аккаунтов
      const { count: sipCount } = await supabase
        .from('sip_accounts')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active')

      // Получаем статистику DID номеров
      const { count: didCount } = await supabase
        .from('did_numbers')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'assigned')

      // Получаем статистику тикетов
      const { count: ticketsCount } = await supabase
        .from('tickets')
        .select('*', { count: 'exact', head: true })
        .in('status', ['open', 'in_progress'])

      setStats({
        totalClients: clientsCount || 0,
        activeSipAccounts: sipCount || 0,
        assignedDidNumbers: didCount || 0,
        openTickets: ticketsCount || 0,
        monthlyRevenue: 15420, // Заглушка
        clientsGrowth: 12.5, // Заглушка
        ticketsResolved: 89, // Заглушка
        systemUptime: 99.9
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Дашборд
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Обзор ключевых метрик платформы
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Активные клиенты"
          value={stats.totalClients}
          icon={Users}
          trend={{ value: stats.clientsGrowth, isPositive: true }}
          color="blue"
        />
        
        <StatCard
          title="SIP аккаунты"
          value={stats.activeSipAccounts}
          icon={Phone}
          color="green"
        />
        
        <StatCard
          title="DID номера"
          value={stats.assignedDidNumbers}
          icon={Hash}
          color="purple"
        />
        
        <StatCard
          title="Открытые тикеты"
          value={stats.openTickets}
          icon={Ticket}
          color="yellow"
        />
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Месячная выручка"
          value={formatCurrency(stats.monthlyRevenue)}
          icon={DollarSign}
          trend={{ value: 8.2, isPositive: true }}
          color="green"
        />
        
        <StatCard
          title="Решено тикетов"
          value={`${stats.ticketsResolved}%`}
          icon={Activity}
          trend={{ value: 2.1, isPositive: true }}
          color="blue"
        />
        
        <StatCard
          title="Время работы"
          value={`${stats.systemUptime}%`}
          icon={TrendingUp}
          color="green"
        />
        
        <StatCard
          title="Новые клиенты"
          value="+23"
          icon={Users}
          trend={{ value: 12.5, isPositive: true }}
          color="purple"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Быстрые действия
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
              Добавить клиента
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Создать тикет
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Назначить номер
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Просмотреть отчеты
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
