# Настройки безопасности для продакшн

## 🔒 Обязательные настройки безопасности

### 1. Supabase Auth настройки

```sql
-- Отключить публичную регистрацию
UPDATE auth.config SET enable_signup = false;

-- Настроить время жизни JWT токенов (1 час)
UPDATE auth.config SET jwt_expiry_limit = 3600;

-- Включить email подтверждение
UPDATE auth.config SET enable_confirmations = true;

-- Настроить минимальную длину пароля
UPDATE auth.config SET password_min_length = 12;

-- Включить требование сложности пароля
UPDATE auth.config SET password_requirements = '{
  "min_length": 12,
  "require_uppercase": true,
  "require_lowercase": true,
  "require_numbers": true,
  "require_symbols": true
}';
```

### 2. RLS политики (уже настроены)

Все таблицы имеют включенный Row Level Security:
- ✅ tenants
- ✅ users  
- ✅ clients
- ✅ sip_accounts
- ✅ did_numbers
- ✅ tickets
- ✅ invoices
- ✅ payments
- ✅ activity_logs

### 3. CORS настройки

В Supabase Dashboard → Settings → API:

```json
{
  "allowed_origins": [
    "https://yourdomain.com",
    "https://www.yourdomain.com"
  ],
  "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  "allowed_headers": ["authorization", "content-type", "x-client-info", "apikey"],
  "max_age": 86400
}
```

### 4. Rate Limiting

В Supabase Dashboard → Settings → API → Rate Limiting:

```json
{
  "anonymous": {
    "requests_per_second": 10,
    "requests_per_minute": 100
  },
  "authenticated": {
    "requests_per_second": 50,
    "requests_per_minute": 1000
  }
}
```

### 5. Database настройки

```sql
-- Включить логирование всех подключений
ALTER SYSTEM SET log_connections = 'on';

-- Включить логирование отключений
ALTER SYSTEM SET log_disconnections = 'on';

-- Логировать медленные запросы (>1 секунды)
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Ограничить количество подключений
ALTER SYSTEM SET max_connections = 100;

-- Настроить таймауты
ALTER SYSTEM SET statement_timeout = '30s';
ALTER SYSTEM SET idle_in_transaction_session_timeout = '10min';

-- Перезагрузить конфигурацию
SELECT pg_reload_conf();
```

### 6. Edge Functions безопасность

Добавить в каждую Edge Function:

```typescript
// Проверка авторизации
const authHeader = req.headers.get('Authorization')
if (!authHeader?.startsWith('Bearer ')) {
  return new Response('Unauthorized', { status: 401 })
}

// Проверка JWT токена
const jwt = authHeader.replace('Bearer ', '')
const { data: user, error } = await supabaseClient.auth.getUser(jwt)
if (error || !user) {
  return new Response('Invalid token', { status: 401 })
}

// Проверка ролей
const allowedRoles = ['provider', 'admin']
if (!allowedRoles.includes(user.user_metadata?.role)) {
  return new Response('Forbidden', { status: 403 })
}

// Rate limiting (простая реализация)
const clientId = req.headers.get('x-client-id') || user.id
const rateLimitKey = `rate_limit:${clientId}`
// Реализация rate limiting с Redis или Supabase
```

### 7. Webhook безопасность

```typescript
// Проверка подписи webhook
function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex')
  
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}

// В Edge Function
const signature = req.headers.get('x-spaas-signature')
const payload = await req.text()
const secret = Deno.env.get('WEBHOOK_SECRET')

if (!verifyWebhookSignature(payload, signature, secret)) {
  return new Response('Invalid signature', { status: 401 })
}
```

### 8. Переменные окружения

Создать `.env.production`:

```env
# Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# App
VITE_APP_NAME=SPaaS Platform
VITE_APP_ENVIRONMENT=production
VITE_ENABLE_DEMO_MODE=false
VITE_ENABLE_ANALYTICS=true

# Security
VITE_ENABLE_CSP=true
VITE_ENABLE_HSTS=true
```

### 9. Content Security Policy (CSP)

Добавить в `index.html`:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://*.supabase.co wss://*.supabase.co;
  frame-ancestors 'none';
  base-uri 'self';
  form-action 'self';
">
```

### 10. HTTP Security Headers

Настроить в веб-сервере (Nginx/Apache):

```nginx
# HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# XSS Protection
add_header X-XSS-Protection "1; mode=block" always;

# Content Type Options
add_header X-Content-Type-Options "nosniff" always;

# Frame Options
add_header X-Frame-Options "DENY" always;

# Referrer Policy
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Permissions Policy
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
```

### 11. Мониторинг и алерты

```sql
-- Создать функцию для мониторинга подозрительной активности
CREATE OR REPLACE FUNCTION monitor_suspicious_activity()
RETURNS TRIGGER AS $$
BEGIN
  -- Множественные неудачные попытки входа
  IF NEW.action = 'LOGIN_FAILED' THEN
    PERFORM pg_notify('security_alert', json_build_object(
      'type', 'multiple_failed_logins',
      'user_id', NEW.user_id,
      'ip_address', NEW.ip_address,
      'count', (
        SELECT COUNT(*) 
        FROM activity_logs 
        WHERE action = 'LOGIN_FAILED' 
        AND ip_address = NEW.ip_address 
        AND created_at > NOW() - INTERVAL '15 minutes'
      )
    )::text);
  END IF;
  
  -- Подозрительные изменения данных
  IF NEW.action IN ('DELETE', 'UPDATE') AND NEW.table_name IN ('users', 'tenants') THEN
    PERFORM pg_notify('security_alert', json_build_object(
      'type', 'sensitive_data_change',
      'user_id', NEW.user_id,
      'table_name', NEW.table_name,
      'record_id', NEW.record_id
    )::text);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Создать триггер
CREATE TRIGGER monitor_suspicious_activity_trigger
  AFTER INSERT ON activity_logs
  FOR EACH ROW
  EXECUTE FUNCTION monitor_suspicious_activity();
```

### 12. Backup и восстановление

```bash
#!/bin/bash
# backup.sh - Скрипт резервного копирования

# Переменные
SUPABASE_PROJECT_ID="your-project-id"
BACKUP_DIR="/backups/spaas"
DATE=$(date +%Y%m%d_%H%M%S)

# Создать директорию
mkdir -p $BACKUP_DIR

# Backup базы данных
pg_dump "postgresql://postgres:[password]@db.[project-id].supabase.co:5432/postgres" \
  --no-owner --no-privileges \
  > "$BACKUP_DIR/database_$DATE.sql"

# Backup Storage (если используется)
# supabase storage download --recursive bucket_name "$BACKUP_DIR/storage_$DATE/"

# Удалить старые бэкапы (старше 30 дней)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/database_$DATE.sql"
```

### 13. Чек-лист безопасности

- [ ] Отключена публичная регистрация
- [ ] Настроены сложные пароли
- [ ] Включен email confirmation
- [ ] Настроены CORS политики
- [ ] Включен Rate Limiting
- [ ] Настроены RLS политики
- [ ] Добавлены HTTP security headers
- [ ] Настроен CSP
- [ ] Включено логирование
- [ ] Настроен мониторинг
- [ ] Настроены алерты
- [ ] Настроены бэкапы
- [ ] Проведен security audit
- [ ] Настроен SSL/TLS
- [ ] Обновлены все зависимости

### 14. Регулярные задачи безопасности

**Еженедельно:**
- Проверка логов на подозрительную активность
- Обновление зависимостей
- Проверка бэкапов

**Ежемесячно:**
- Ротация API ключей
- Аудит пользователей и ролей
- Проверка настроек безопасности

**Ежеквартально:**
- Полный security audit
- Тестирование восстановления из бэкапов
- Обновление политик безопасности

### 15. Контакты для инцидентов

```
Security Team: <EMAIL>
Emergency: +1-XXX-XXX-XXXX
Incident Response: https://yourcompany.com/security-incident
```
