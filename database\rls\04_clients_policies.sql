-- RLS политики для таблицы clients

-- Provider может видеть и управлять всеми клиентами
CREATE POLICY "Provider full access to clients" ON clients
  FOR ALL 
  USING (auth.is_provider())
  WITH CHECK (auth.is_provider());

-- Пользователи арендатора могут видеть клиентов своего арендатора
CREATE POLICY "Tenant users can view clients" ON clients
  FOR SELECT 
  USING (
    auth.has_tenant_access(tenant_id)
  );

-- Администраторы арендатора могут управлять клиентами
CREATE POLICY "Tenant admins can manage clients" ON clients
  FOR ALL 
  USING (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    NOT auth.is_provider()
    AND auth.is_tenant_admin()
    AND tenant_id = auth.user_tenant_id()
  );

-- Support может видеть и обновлять клиентов (но не создавать/удалять)
CREATE POLICY "Support can view and update clients" ON clients
  FOR SELECT 
  USING (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  );

CREATE POLICY "Support can update clients" ON clients
  FOR UPDATE 
  USING (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  )
  WITH CHECK (
    auth.user_role() = 'support'
    AND tenant_id = auth.user_tenant_id()
  );

-- Staff может только просматривать клиентов
CREATE POLICY "Staff can view clients" ON clients
  FOR SELECT 
  USING (
    auth.user_role() = 'staff'
    AND tenant_id = auth.user_tenant_id()
  );

-- Клиенты могут видеть только свою информацию
CREATE POLICY "Clients can view own info" ON clients
  FOR SELECT 
  USING (
    auth.user_role() = 'client'
    AND EXISTS (
      SELECT 1 FROM users u 
      WHERE u.user_id = auth.user_id() 
      AND u.tenant_id = clients.tenant_id
      -- Дополнительная связь между user и client может быть через custom поле
    )
  );

-- Клиенты могут обновлять ограниченную информацию о себе
CREATE POLICY "Clients can update own limited info" ON clients
  FOR UPDATE 
  USING (
    auth.user_role() = 'client'
    AND EXISTS (
      SELECT 1 FROM users u 
      WHERE u.user_id = auth.user_id() 
      AND u.tenant_id = clients.tenant_id
    )
  )
  WITH CHECK (
    auth.user_role() = 'client'
    AND EXISTS (
      SELECT 1 FROM users u 
      WHERE u.user_id = auth.user_id() 
      AND u.tenant_id = clients.tenant_id
    )
    -- Ограничиваем поля, которые клиент может изменить
    AND OLD.tenant_id = NEW.tenant_id
    AND OLD.client_code = NEW.client_code
    AND OLD.status = NEW.status
    AND OLD.billing_type = NEW.billing_type
    AND OLD.credit_limit = NEW.credit_limit
    AND OLD.current_balance = NEW.current_balance
  );

-- Запрет на создание клиентов для не-администраторов
CREATE POLICY "Only admins can create clients" ON clients
  FOR INSERT 
  WITH CHECK (
    auth.is_provider() 
    OR (
      auth.is_tenant_admin()
      AND tenant_id = auth.user_tenant_id()
    )
  );

-- Запрет на удаление клиентов для не-администраторов
CREATE POLICY "Only admins can delete clients" ON clients
  FOR DELETE 
  USING (
    auth.is_provider() 
    OR (
      auth.is_tenant_admin()
      AND tenant_id = auth.user_tenant_id()
    )
  );

-- Комментарии к политикам
COMMENT ON POLICY "Provider full access to clients" ON clients IS 
  'Provider имеет полный доступ ко всем клиентам';

COMMENT ON POLICY "Tenant users can view clients" ON clients IS 
  'Пользователи арендатора могут просматривать клиентов своего арендатора';

COMMENT ON POLICY "Tenant admins can manage clients" ON clients IS 
  'Администраторы арендатора могут управлять клиентами';

COMMENT ON POLICY "Support can view and update clients" ON clients IS 
  'Support может просматривать и обновлять клиентов';

COMMENT ON POLICY "Staff can view clients" ON clients IS 
  'Staff может только просматривать клиентов';

COMMENT ON POLICY "Clients can view own info" ON clients IS 
  'Клиенты могут видеть только свою информацию';

COMMENT ON POLICY "Clients can update own limited info" ON clients IS 
  'Клиенты могут обновлять ограниченную информацию о себе';
