#!/bin/bash

# Скрипт для первоначальной настройки SPaaS платформы

echo "🚀 Настройка SPaaS платформы..."

# Проверяем наличие Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js не найден. Пожалуйста, установите Node.js 18+ и попробуйте снова."
    exit 1
fi

# Проверяем версию Node.js
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Требуется Node.js версии 18 или выше. Текущая версия: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) найден"

# Проверяем наличие Supabase CLI
if ! command -v supabase &> /dev/null; then
    echo "⚠️  Supabase CLI не найден. Устанавливаем..."
    npm install -g supabase
fi

echo "✅ Supabase CLI готов"

# Переходим в папку frontend и устанавливаем зависимости
echo "📦 Установка зависимостей фронтенда..."
cd frontend
npm install

echo "✅ Зависимости фронтенда установлены"

# Возвращаемся в корневую папку
cd ..

# Создаем .env файл если его нет
if [ ! -f "frontend/.env" ]; then
    echo "📝 Создание файла конфигурации..."
    cp frontend/.env.example frontend/.env
    echo "⚠️  Пожалуйста, отредактируйте frontend/.env файл с вашими настройками Supabase"
fi

echo ""
echo "🎉 Настройка завершена!"
echo ""
echo "Следующие шаги:"
echo "1. Настройте ваш проект в Supabase (https://supabase.com)"
echo "2. Отредактируйте frontend/.env файл с вашими настройками"
echo "3. Запустите миграции базы данных: npm run db:migrate"
echo "4. Запустите фронтенд: npm run dev"
echo ""
echo "Документация: docs/architecture/"
