import React, { useState, useEffect } from 'react'
import { FileText, Download, Calendar, Filter, Users, Phone, DollarSign, Hash, Ticket, Clock, Search, Plus, Eye, Trash2 } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatCurrency, formatDate } from '@/lib/utils'

interface Report {
  report_id: string
  name: string
  description: string
  type: 'financial' | 'operational' | 'technical' | 'custom'
  format: 'pdf' | 'excel' | 'csv'
  schedule: 'manual' | 'daily' | 'weekly' | 'monthly'
  status: 'ready' | 'generating' | 'scheduled' | 'error'
  created_at: string
  generated_at?: string
  file_size?: number
  parameters: {
    date_from: string
    date_to: string
    filters?: any
  }
}

interface ReportTemplate {
  template_id: string
  name: string
  description: string
  type: 'financial' | 'operational' | 'technical' | 'custom'
  icon: string
  color: string
  fields: string[]
  estimated_time: string
}

// Демо данные шаблонов отчетов
const reportTemplates: ReportTemplate[] = [
  {
    template_id: 'financial-summary',
    name: 'Финансовый отчет',
    description: 'Сводка доходов, расходов и прибыли за период',
    type: 'financial',
    icon: '💰',
    color: 'bg-green-500',
    fields: ['Доходы', 'Расходы', 'Прибыль', 'Клиенты', 'Инвойсы'],
    estimated_time: '2-3 мин'
  },
  {
    template_id: 'client-activity',
    name: 'Активность клиентов',
    description: 'Статистика использования услуг клиентами',
    type: 'operational',
    icon: '👥',
    color: 'bg-blue-500',
    fields: ['Клиенты', 'SIP аккаунты', 'DID номера', 'Звонки', 'Трафик'],
    estimated_time: '1-2 мин'
  },
  {
    template_id: 'call-statistics',
    name: 'Статистика звонков',
    description: 'Детальная статистика входящих и исходящих звонков',
    type: 'technical',
    icon: '📞',
    color: 'bg-purple-500',
    fields: ['Входящие', 'Исходящие', 'Длительность', 'Качество', 'Ошибки'],
    estimated_time: '3-5 мин'
  },
  {
    template_id: 'billing-report',
    name: 'Биллинговый отчет',
    description: 'Отчет по выставленным счетам и платежам',
    type: 'financial',
    icon: '🧾',
    color: 'bg-indigo-500',
    fields: ['Инвойсы', 'Платежи', 'Задолженности', 'Тарифы'],
    estimated_time: '2-3 мин'
  },
  {
    template_id: 'technical-health',
    name: 'Техническое состояние',
    description: 'Мониторинг работоспособности системы',
    type: 'technical',
    icon: '⚙️',
    color: 'bg-gray-600',
    fields: ['Uptime', 'Ошибки', 'Производительность', 'Ресурсы'],
    estimated_time: '1-2 мин'
  },
  {
    template_id: 'support-tickets',
    name: 'Отчет по тикетам',
    description: 'Статистика обращений в службу поддержки',
    type: 'operational',
    icon: '🎫',
    color: 'bg-yellow-500',
    fields: ['Тикеты', 'Категории', 'Время решения', 'Рейтинги'],
    estimated_time: '1-2 мин'
  }
]

// Демо данные сгенерированных отчетов
const demoReports: Report[] = [
  {
    report_id: 'rep-001',
    name: 'Финансовый отчет за ноябрь 2024',
    description: 'Сводка доходов, расходов и прибыли за ноябрь',
    type: 'financial',
    format: 'pdf',
    schedule: 'monthly',
    status: 'ready',
    created_at: '2024-12-01T09:00:00Z',
    generated_at: '2024-12-01T09:03:00Z',
    file_size: 2456789,
    parameters: {
      date_from: '2024-11-01',
      date_to: '2024-11-30'
    }
  },
  {
    report_id: 'rep-002',
    name: 'Активность клиентов за неделю',
    description: 'Статистика использования услуг за последнюю неделю',
    type: 'operational',
    format: 'excel',
    schedule: 'weekly',
    status: 'ready',
    created_at: '2024-11-30T18:00:00Z',
    generated_at: '2024-11-30T18:02:00Z',
    file_size: 1234567,
    parameters: {
      date_from: '2024-11-24',
      date_to: '2024-11-30'
    }
  },
  {
    report_id: 'rep-003',
    name: 'Статистика звонков за декабрь',
    description: 'Детальная статистика звонков',
    type: 'technical',
    format: 'csv',
    schedule: 'manual',
    status: 'generating',
    created_at: '2024-12-01T15:30:00Z',
    parameters: {
      date_from: '2024-12-01',
      date_to: '2024-12-01'
    }
  },
  {
    report_id: 'rep-004',
    name: 'Биллинговый отчет Q4 2024',
    description: 'Квартальный отчет по биллингу',
    type: 'financial',
    format: 'pdf',
    schedule: 'manual',
    status: 'scheduled',
    created_at: '2024-12-01T10:00:00Z',
    parameters: {
      date_from: '2024-10-01',
      date_to: '2024-12-31'
    }
  },
  {
    report_id: 'rep-005',
    name: 'Техническое состояние системы',
    description: 'Ежедневный мониторинг',
    type: 'technical',
    format: 'pdf',
    schedule: 'daily',
    status: 'error',
    created_at: '2024-12-01T06:00:00Z',
    parameters: {
      date_from: '2024-11-30',
      date_to: '2024-12-01'
    }
  }
]

export const Reports: React.FC = () => {
  const { user } = useAuthStore()
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)

  useEffect(() => {
    fetchReports()
  }, [user])

  const fetchReports = async () => {
    if (!user) return

    try {
      setLoading(true)

      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setReports(demoReports)
      } else {
        // Здесь будет реальный запрос к Supabase
        setReports([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredReports = reports.filter(report => {
    const matchesSearch =
      report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = typeFilter === 'all' || report.type === typeFilter
    const matchesStatus = statusFilter === 'all' || report.status === statusFilter

    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <Download className="h-4 w-4 text-green-600" />
      case 'generating':
        return <Clock className="h-4 w-4 text-blue-600" />
      case 'scheduled':
        return <Calendar className="h-4 w-4 text-yellow-600" />
      case 'error':
        return <Trash2 className="h-4 w-4 text-red-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ready: { label: 'Готов', className: 'badge-success' },
      generating: { label: 'Генерируется', className: 'badge-primary' },
      scheduled: { label: 'Запланирован', className: 'badge-warning' },
      error: { label: 'Ошибка', className: 'badge-danger' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ready

    return (
      <span className={`badge ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'financial':
        return '💰'
      case 'operational':
        return '👥'
      case 'technical':
        return '⚙️'
      case 'custom':
        return '📋'
      default:
        return '📄'
    }
  }

  const getTypeName = (type: string) => {
    const types = {
      financial: 'Финансовый',
      operational: 'Операционный',
      technical: 'Технический',
      custom: 'Пользовательский'
    }
    return types[type as keyof typeof types] || type
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleGenerateReport = (template: ReportTemplate) => {
    // В реальном приложении здесь будет запрос к API
    const newReport: Report = {
      report_id: `rep-${Date.now()}`,
      name: `${template.name} - ${formatDate(new Date().toISOString(), 'short')}`,
      description: template.description,
      type: template.type,
      format: 'pdf',
      schedule: 'manual',
      status: 'generating',
      created_at: new Date().toISOString(),
      parameters: {
        date_from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_to: new Date().toISOString().split('T')[0]
      }
    }

    setReports(prev => [newReport, ...prev])

    // Симуляция генерации отчета
    setTimeout(() => {
      setReports(prev => prev.map(report =>
        report.report_id === newReport.report_id
          ? {
              ...report,
              status: 'ready',
              generated_at: new Date().toISOString(),
              file_size: Math.floor(Math.random() * 5000000) + 500000
            }
          : report
      ))
    }, 3000)
  }

  const handleDownloadReport = (reportId: string) => {
    // В реальном приложении здесь будет скачивание файла
    alert(`Скачивание отчета ${reportId}`)
  }

  const handleDeleteReport = (reportId: string) => {
    if (confirm('Вы уверены, что хотите удалить этот отчет?')) {
      setReports(prev => prev.filter(r => r.report_id !== reportId))
    }
  }

  const getStats = () => {
    const ready = reports.filter(r => r.status === 'ready').length
    const generating = reports.filter(r => r.status === 'generating').length
    const scheduled = reports.filter(r => r.status === 'scheduled').length
    const errors = reports.filter(r => r.status === 'error').length

    return { ready, generating, scheduled, errors, total: reports.length }
  }

  const stats = getStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Отчеты</h1>
          <p className="mt-1 text-sm text-gray-500">
            Генерация и управление отчетами
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          Создать отчет
        </button>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon green">
            <Download className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Готовые отчеты</h3>
            <p>{stats.ready}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon blue">
            <Clock className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Генерируются</h3>
            <p>{stats.generating}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <Calendar className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Запланированы</h3>
            <p>{stats.scheduled}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon red">
            <Trash2 className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Ошибки</h3>
            <p>{stats.errors}</p>
          </div>
        </div>
      </div>

      {/* Report Templates */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Шаблоны отчетов</h3>
          <p className="text-sm text-gray-500">Выберите тип отчета для генерации</p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {reportTemplates.map((template) => (
              <div key={template.template_id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${template.color} rounded-lg flex items-center justify-center text-white text-lg mr-3`}>
                      {template.icon}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                      <div className="text-xs text-gray-500">{getTypeName(template.type)}</div>
                    </div>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-3">{template.description}</p>

                <div className="mb-3">
                  <div className="text-xs font-medium text-gray-700 mb-1">Включает:</div>
                  <div className="flex flex-wrap gap-1">
                    {template.fields.slice(0, 3).map((field, index) => (
                      <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {field}
                      </span>
                    ))}
                    {template.fields.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{template.fields.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    ⏱️ {template.estimated_time}
                  </div>
                  <button
                    onClick={() => handleGenerateReport(template)}
                    className="btn btn-primary text-xs"
                  >
                    Создать
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Generated Reports */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Сгенерированные отчеты</h3>
              <p className="text-sm text-gray-500">История созданных отчетов</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="sm:w-64">
              <input
                type="text"
                placeholder="Поиск отчетов..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input"
              />
            </div>
            <div className="sm:w-48">
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все типы</option>
                <option value="financial">Финансовые</option>
                <option value="operational">Операционные</option>
                <option value="technical">Технические</option>
                <option value="custom">Пользовательские</option>
              </select>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все статусы</option>
                <option value="ready">Готовые</option>
                <option value="generating">Генерируются</option>
                <option value="scheduled">Запланированы</option>
                <option value="error">Ошибки</option>
              </select>
            </div>
          </div>

          {/* Reports List */}
          <div className="space-y-4">
            {filteredReports.map((report) => (
              <div key={report.report_id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">
                      {getTypeIcon(report.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{report.name}</h4>
                        {getStatusBadge(report.status)}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{report.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Создан: {formatDate(report.created_at, 'short')}</span>
                        {report.generated_at && (
                          <span>Готов: {formatDate(report.generated_at, 'short')}</span>
                        )}
                        {report.file_size && (
                          <span>Размер: {formatFileSize(report.file_size)}</span>
                        )}
                        <span>Формат: {report.format.toUpperCase()}</span>
                        <span>Тип: {getTypeName(report.type)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(report.status)}
                    {report.status === 'ready' && (
                      <button
                        onClick={() => handleDownloadReport(report.report_id)}
                        className="btn btn-secondary text-xs"
                        title="Скачать отчет"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Скачать
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteReport(report.report_id)}
                      className="p-1 text-gray-400 hover:text-red-600"
                      title="Удалить отчет"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {filteredReports.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <div className="text-gray-500">
                  {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
                    ? 'Отчеты не найдены по заданным критериям'
                    : 'Пока нет сгенерированных отчетов'
                  }
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
