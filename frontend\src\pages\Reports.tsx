import React, { useState } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts'
import { Calendar, Download, Filter, TrendingUp, TrendingDown, Users, Phone, DollarSign, FileText } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatCurrency } from '@/lib/utils'

// Демо данные для графиков
const revenueData = [
  { month: 'Янв', revenue: 85000, clients: 12 },
  { month: 'Фев', revenue: 92000, clients: 15 },
  { month: 'Мар', revenue: 78000, clients: 11 },
  { month: 'Апр', revenue: 105000, clients: 18 },
  { month: 'Май', revenue: 118000, clients: 22 },
  { month: 'Июн', revenue: 125000, clients: 25 },
  { month: 'Июл', revenue: 132000, clients: 28 },
  { month: 'Авг', revenue: 128000, clients: 26 },
  { month: 'Сен', revenue: 145000, clients: 32 },
  { month: 'Окт', revenue: 138000, clients: 30 },
  { month: 'Ноя', revenue: 152000, clients: 35 },
  { month: 'Дек', revenue: 165000, clients: 38 }
]

const callsData = [
  { day: 'Пн', incoming: 245, outgoing: 189 },
  { day: 'Вт', incoming: 312, outgoing: 234 },
  { day: 'Ср', incoming: 289, outgoing: 198 },
  { day: 'Чт', incoming: 356, outgoing: 267 },
  { day: 'Пт', incoming: 398, outgoing: 301 },
  { day: 'Сб', incoming: 178, outgoing: 134 },
  { day: 'Вс', incoming: 156, outgoing: 98 }
]

const serviceDistribution = [
  { name: 'SIP Аккаунты', value: 45, color: '#3b82f6' },
  { name: 'DID Номера', value: 30, color: '#10b981' },
  { name: 'Исходящие звонки', value: 20, color: '#f59e0b' },
  { name: 'Дополнительные услуги', value: 5, color: '#ef4444' }
]

const ticketStats = [
  { category: 'Техническая поддержка', open: 12, resolved: 45 },
  { category: 'Биллинг', open: 3, resolved: 28 },
  { category: 'Настройка', open: 8, resolved: 32 },
  { category: 'Другое', open: 2, resolved: 15 }
]

export const Reports: React.FC = () => {
  const { user } = useAuthStore()
  const [dateRange, setDateRange] = useState('last_month')
  const [reportType, setReportType] = useState('overview')

  const currentMonth = revenueData[revenueData.length - 1]
  const previousMonth = revenueData[revenueData.length - 2]
  const revenueGrowth = ((currentMonth.revenue - previousMonth.revenue) / previousMonth.revenue * 100).toFixed(1)
  const clientsGrowth = ((currentMonth.clients - previousMonth.clients) / previousMonth.clients * 100).toFixed(1)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Отчеты и аналитика</h1>
          <p className="mt-1 text-sm text-gray-500">
            Анализ производительности и финансовых показателей
          </p>
        </div>
        <div className="flex gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="form-input"
          >
            <option value="last_week">Последняя неделя</option>
            <option value="last_month">Последний месяц</option>
            <option value="last_quarter">Последний квартал</option>
            <option value="last_year">Последний год</option>
          </select>
          <button className="btn btn-primary">
            <Download className="h-4 w-4 mr-2" />
            Экспорт
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon blue">
            <DollarSign className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Выручка за месяц</h3>
            <p>{formatCurrency(currentMonth.revenue)}</p>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+{revenueGrowth}%</span>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <Users className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Активные клиенты</h3>
            <p>{currentMonth.clients}</p>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+{clientsGrowth}%</span>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <Phone className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Звонков за неделю</h3>
            <p>{callsData.reduce((sum, day) => sum + day.incoming + day.outgoing, 0)}</p>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">****%</span>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <FileText className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Открытые тикеты</h3>
            <p>{ticketStats.reduce((sum, cat) => sum + cat.open, 0)}</p>
            <div className="flex items-center mt-1">
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600">-12.5%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Выручка по месяцам</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `${value / 1000}k`} />
                <Tooltip 
                  formatter={(value: number) => [formatCurrency(value), 'Выручка']}
                  labelStyle={{ color: '#374151' }}
                />
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Calls Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Звонки по дням недели</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={callsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="incoming" fill="#10b981" name="Входящие" />
                <Bar dataKey="outgoing" fill="#3b82f6" name="Исходящие" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Service Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Распределение услуг</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={serviceDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {serviceDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [`${value}%`, 'Доля']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Ticket Statistics */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Статистика тикетов</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={ticketStats} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="category" type="category" width={120} />
                <Tooltip />
                <Bar dataKey="open" fill="#f59e0b" name="Открытые" />
                <Bar dataKey="resolved" fill="#10b981" name="Решенные" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Detailed Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Clients */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Топ клиенты по выручке</h3>
          </div>
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>Клиент</th>
                  <th>Выручка</th>
                  <th>Рост</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>ООО "Демо Компания"</td>
                  <td>{formatCurrency(45000)}</td>
                  <td className="text-green-600">+15%</td>
                </tr>
                <tr>
                  <td>ЗАО "Технологии Связи"</td>
                  <td>{formatCurrency(38000)}</td>
                  <td className="text-green-600">+8%</td>
                </tr>
                <tr>
                  <td>ИП Сидоров А.В.</td>
                  <td>{formatCurrency(22000)}</td>
                  <td className="text-red-600">-3%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Последняя активность</h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              <div className="flex items-center justify-between py-2 border-b border-gray-100">
                <div>
                  <p className="text-sm font-medium">Новый клиент зарегистрирован</p>
                  <p className="text-xs text-gray-500">2 часа назад</p>
                </div>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-gray-100">
                <div>
                  <p className="text-sm font-medium">Оплачен инвойс INV-2024-002</p>
                  <p className="text-xs text-gray-500">4 часа назад</p>
                </div>
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-gray-100">
                <div>
                  <p className="text-sm font-medium">Создан тикет T20241201-0003</p>
                  <p className="text-xs text-gray-500">6 часов назад</p>
                </div>
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              </div>
              <div className="flex items-center justify-between py-2">
                <div>
                  <p className="text-sm font-medium">Настроен новый SIP аккаунт</p>
                  <p className="text-xs text-gray-500">8 часов назад</p>
                </div>
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
