# 📹 Видеоплеер для WebRTC звонков

## Обзор

Реализован профессиональный видеоплеер для WebRTC звонков с поддержкой различных режимов отображения, управления медиа-потоками и интерактивными элементами управления.

## ✨ Компоненты

### 1. VideoPlayer
Полнофункциональный видеоплеер для видеозвонков с:
- **Локальное и удаленное видео** - отображение обеих камер
- **Режимы отображения** - фокус, рядом, картинка-в-картинке
- **Управление медиа** - звук, видео, полноэкранный режим
- **Интерактивные элементы** - таймер, статусы, индикаторы

### 2. AudioPlayer
Специализированный плеер для голосовых звонков с:
- **Визуализация аудио** - уровни звука в реальном времени
- **Аватар участника** - с анимацией при разговоре
- **Управление звуком** - микрофон, динамики, удержание
- **Статусы соединения** - качество, шифрование

## 🎯 Режимы отображения

### 📺 Фокус на собеседнике (focus-remote)
```
┌─────────────────────────────────────────┐
│ [Удаленное видео - полный экран]        │
│                                         │
│                                         │
│                                         │
│                    ┌─────────┐          │
│                    │Локальное│          │
│                    │  видео  │          │
│                    └─────────┘          │
│ [Элементы управления]                   │
└─────────────────────────────────────────┘
```

### 📱 Рядом (side-by-side)
```
┌─────────────────────────────────────────┐
│ ┌─────────────────┐ ┌─────────────────┐ │
│ │                 │ │                 │ │
│ │  Удаленное      │ │   Локальное     │ │
│ │    видео        │ │     видео       │ │
│ │                 │ │                 │ │
│ └─────────────────┘ └─────────────────┘ │
│ [Элементы управления]                   │
└─────────────────────────────────────────┘
```

### 🖼️ Картинка в картинке (picture-in-picture)
```
┌─────────────────────────────────────────┐
│ [Удаленное видео - основной экран]      │
│                                         │
│                          ┌───────────┐  │
│                          │ Локальное │  │
│                          │   видео   │  │
│                          │ (большое) │  │
│                          └───────────┘  │
│ [Элементы управления]                   │
└─────────────────────────────────────────┘
```

## 🎮 Элементы управления

### Основные кнопки:
- **🎤 Микрофон** - включить/выключить звук
- **📹 Видео** - включить/выключить камеру
- **🔊 Динамики** - управление звуком собеседника
- **⛶ Полноэкранный** - развернуть на весь экран
- **📺 PiP** - режим картинка-в-картинке
- **📞 Завершить** - закончить звонок

### Дополнительные функции:
- **Изменение размера** локального видео
- **Переключение режимов** отображения
- **Автоскрытие** элементов в полноэкранном режиме
- **Индикаторы статуса** подключения

## 🔧 Технические особенности

### VideoPlayer Props
```typescript
interface VideoPlayerProps {
  localStream?: MediaStream          // Локальный поток
  remoteStream?: MediaStream         // Удаленный поток
  isLocalMuted?: boolean            // Микрофон выключен
  isRemoteAudioEnabled?: boolean    // Звук собеседника
  isLocalVideoEnabled?: boolean     // Локальное видео
  isRemoteVideoEnabled?: boolean    // Удаленное видео
  participantName?: string          // Имя участника
  participantNumber?: string        // Номер телефона
  callDuration?: number            // Длительность звонка
  onToggleLocalMute?: () => void   // Переключить микрофон
  onToggleLocalVideo?: () => void  // Переключить видео
  onToggleRemoteAudio?: () => void // Переключить звук
  onEndCall?: () => void           // Завершить звонок
  onToggleFullscreen?: () => void  // Полноэкранный режим
  onTogglePiP?: () => void         // Picture-in-Picture
  className?: string               // CSS классы
}
```

### AudioPlayer Props
```typescript
interface AudioPlayerProps {
  localStream?: MediaStream         // Локальный аудио поток
  remoteStream?: MediaStream        // Удаленный аудио поток
  isLocalMuted?: boolean           // Микрофон выключен
  isRemoteAudioEnabled?: boolean   // Звук собеседника
  participantName?: string         // Имя участника
  participantNumber?: string       // Номер телефона
  callDuration?: number           // Длительность звонка
  callDirection?: 'inbound' | 'outbound' // Направление
  onToggleLocalMute?: () => void  // Переключить микрофон
  onToggleRemoteAudio?: () => void // Переключить звук
  onEndCall?: () => void          // Завершить звонок
  onHold?: () => void             // Удержание звонка
  isOnHold?: boolean              // На удержании
  className?: string              // CSS классы
}
```

## 🎨 Визуальные эффекты

### VideoPlayer:
- **Градиентные оверлеи** для элементов управления
- **Плавные переходы** между режимами
- **Анимированные индикаторы** статуса
- **Адаптивная сетка** для разных экранов

### AudioPlayer:
- **Пульсирующий аватар** при разговоре
- **Анимированные уровни** звука
- **Цветовая индикация** статусов
- **Плавные переходы** состояний

## 🔊 Аудио визуализация

### Анализ аудио потока:
```typescript
const audioContext = new AudioContext()
const analyser = audioContext.createAnalyser()
const source = audioContext.createMediaStreamSource(localStream)

source.connect(analyser)
analyser.fftSize = 256

const bufferLength = analyser.frequencyBinCount
const dataArray = new Uint8Array(bufferLength)

const updateAudioLevel = () => {
  analyser.getByteFrequencyData(dataArray)
  const average = dataArray.reduce((a, b) => a + b) / bufferLength
  setAudioLevel(average / 255)
  requestAnimationFrame(updateAudioLevel)
}
```

### Визуальные индикаторы:
- **Полосы уровня** звука для микрофона и динамиков
- **Цветовая кодировка** (зеленый - активен, красный - выключен)
- **Анимация аватара** в зависимости от уровня звука
- **Иконки состояния** (микрофон, динамики)

## 📱 Адаптивность

### Мобильные устройства:
- **Сенсорное управление** всех элементов
- **Адаптивные размеры** кнопок и видео
- **Оптимизированные жесты** для управления
- **Автоповорот** экрана

### Различные экраны:
- **Responsive grid** для видео
- **Масштабируемые элементы** управления
- **Адаптивные шрифты** и иконки
- **Гибкие отступы** и размеры

## 🔒 Безопасность и производительность

### Управление потоками:
- **Автоматическая очистка** MediaStream при завершении
- **Обработка ошибок** доступа к устройствам
- **Оптимизация памяти** для видео элементов
- **Graceful degradation** при отсутствии устройств

### Производительность:
- **Ленивая инициализация** аудио контекста
- **Оптимизированная отрисовка** canvas для демо
- **Throttled обновления** уровней звука
- **Эффективное управление** DOM элементами

## 🎯 Использование

### Интеграция в звонки:
```typescript
// Видеозвонок
{isVideoOn || remoteStream ? (
  <VideoPlayer
    localStream={localStream}
    remoteStream={remoteStream}
    isLocalMuted={isMuted}
    isRemoteAudioEnabled={isRemoteAudioEnabled}
    isLocalVideoEnabled={isVideoOn}
    participantName={currentCall.name}
    participantNumber={currentCall.number}
    callDuration={callDuration}
    onToggleLocalMute={toggleMute}
    onToggleLocalVideo={toggleVideo}
    onToggleRemoteAudio={toggleRemoteAudio}
    onEndCall={endCall}
  />
) : (
  // Голосовой звонок
  <AudioPlayer
    localStream={localStream}
    remoteStream={remoteStream}
    isLocalMuted={isMuted}
    isRemoteAudioEnabled={isRemoteAudioEnabled}
    participantName={currentCall.name}
    participantNumber={currentCall.number}
    callDuration={callDuration}
    callDirection={currentCall.direction}
    onToggleLocalMute={toggleMute}
    onToggleRemoteAudio={toggleRemoteAudio}
    onEndCall={endCall}
  />
)}
```

## 🔮 Будущие улучшения

### Функциональность:
- **Запись звонков** с согласия участников
- **Снимки экрана** во время видеозвонков
- **Фильтры и эффекты** для видео
- **Виртуальные фоны** для камеры

### UX улучшения:
- **Жесты управления** (свайпы, пинч)
- **Голосовые команды** для управления
- **Настраиваемые темы** интерфейса
- **Accessibility** поддержка

### Технические:
- **WebCodecs API** для лучшего качества
- **WebAssembly** для обработки видео
- **Service Workers** для фонового режима
- **WebRTC Insertable Streams** для эффектов

## 📞 Интеграция с SignalWire

Плееры готовы для интеграции с реальными WebRTC потоками:

```typescript
// Получение локального потока
const localStream = await navigator.mediaDevices.getUserMedia({
  audio: true,
  video: isVideoEnabled
})

// Получение удаленного потока от SignalWire
call.on('track', (event) => {
  const [remoteStream] = event.streams
  setRemoteStream(remoteStream)
})

// Передача потоков в плеер
<VideoPlayer
  localStream={localStream}
  remoteStream={remoteStream}
  // ... другие props
/>
```

Теперь система звонков имеет профессиональные видео и аудио плееры для полноценных WebRTC звонков! 🎥📞
