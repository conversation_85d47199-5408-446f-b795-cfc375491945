import React, { useState, useEffect } from 'react'
import { Plus, Search, Shield, Edit, Trash2, <PERSON>, Setting<PERSON>, Eye, Lock } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate } from '@/lib/utils'
import { RoleModal } from '@/components/RoleModal'
import { RoleDetailsModal } from '@/components/RoleDetailsModal'

interface Role {
  role_id: string
  name: string
  display_name: string
  description: string
  permissions: string[]
  is_system: boolean
  created_at: string
  updated_at: string
  users_count: number
}

interface Permission {
  id: string
  name: string
  display_name: string
  category: string
  description: string
}

// Демо данные ролей
const demoRoles: Role[] = [
  {
    role_id: 'role-1',
    name: 'provider',
    display_name: 'Провайдер',
    description: 'Полный доступ ко всем функциям системы',
    permissions: ['*'],
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    users_count: 1
  },
  {
    role_id: 'role-2',
    name: 'reseller',
    display_name: 'Реселлер',
    description: 'Управление клиентами и продажами',
    permissions: [
      'clients.read', 'clients.create', 'clients.update',
      'sip_accounts.read', 'sip_accounts.create', 'sip_accounts.update',
      'did_numbers.read', 'did_numbers.assign',
      'billing.read', 'billing.create',
      'tickets.read', 'tickets.create', 'tickets.update',
      'reports.read'
    ],
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    users_count: 3
  },
  {
    role_id: 'role-3',
    name: 'admin',
    display_name: 'Администратор',
    description: 'Административные функции в рамках тенанта',
    permissions: [
      'users.read', 'users.create', 'users.update',
      'clients.read', 'clients.create', 'clients.update', 'clients.delete',
      'sip_accounts.read', 'sip_accounts.create', 'sip_accounts.update', 'sip_accounts.delete',
      'did_numbers.read', 'did_numbers.create', 'did_numbers.update', 'did_numbers.delete',
      'tickets.read', 'tickets.create', 'tickets.update', 'tickets.delete',
      'billing.read', 'billing.create', 'billing.update',
      'reports.read', 'reports.create',
      'settings.read', 'settings.update'
    ],
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    users_count: 2
  },
  {
    role_id: 'role-4',
    name: 'support',
    display_name: 'Поддержка',
    description: 'Техническая поддержка клиентов',
    permissions: [
      'clients.read',
      'sip_accounts.read', 'sip_accounts.update',
      'did_numbers.read',
      'tickets.read', 'tickets.create', 'tickets.update',
      'logs.read'
    ],
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    users_count: 4
  },
  {
    role_id: 'role-5',
    name: 'manager',
    display_name: 'Менеджер',
    description: 'Управление продажами и клиентами',
    permissions: [
      'clients.read', 'clients.create', 'clients.update',
      'sip_accounts.read', 'sip_accounts.create',
      'did_numbers.read', 'did_numbers.assign',
      'billing.read',
      'tickets.read', 'tickets.create',
      'reports.read'
    ],
    is_system: false,
    created_at: '2024-02-15T10:30:00Z',
    updated_at: '2024-02-15T10:30:00Z',
    users_count: 2
  }
]

// Демо данные разрешений
const demoPermissions: Permission[] = [
  // Клиенты
  { id: 'clients.read', name: 'clients.read', display_name: 'Просмотр клиентов', category: 'Клиенты', description: 'Просмотр списка клиентов' },
  { id: 'clients.create', name: 'clients.create', display_name: 'Создание клиентов', category: 'Клиенты', description: 'Создание новых клиентов' },
  { id: 'clients.update', name: 'clients.update', display_name: 'Редактирование клиентов', category: 'Клиенты', description: 'Редактирование данных клиентов' },
  { id: 'clients.delete', name: 'clients.delete', display_name: 'Удаление клиентов', category: 'Клиенты', description: 'Удаление клиентов' },
  
  // SIP аккаунты
  { id: 'sip_accounts.read', name: 'sip_accounts.read', display_name: 'Просмотр SIP', category: 'SIP', description: 'Просмотр SIP аккаунтов' },
  { id: 'sip_accounts.create', name: 'sip_accounts.create', display_name: 'Создание SIP', category: 'SIP', description: 'Создание SIP аккаунтов' },
  { id: 'sip_accounts.update', name: 'sip_accounts.update', display_name: 'Редактирование SIP', category: 'SIP', description: 'Редактирование SIP аккаунтов' },
  { id: 'sip_accounts.delete', name: 'sip_accounts.delete', display_name: 'Удаление SIP', category: 'SIP', description: 'Удаление SIP аккаунтов' },
  
  // DID номера
  { id: 'did_numbers.read', name: 'did_numbers.read', display_name: 'Просмотр номеров', category: 'DID', description: 'Просмотр DID номеров' },
  { id: 'did_numbers.create', name: 'did_numbers.create', display_name: 'Создание номеров', category: 'DID', description: 'Создание DID номеров' },
  { id: 'did_numbers.update', name: 'did_numbers.update', display_name: 'Редактирование номеров', category: 'DID', description: 'Редактирование DID номеров' },
  { id: 'did_numbers.delete', name: 'did_numbers.delete', display_name: 'Удаление номеров', category: 'DID', description: 'Удаление DID номеров' },
  { id: 'did_numbers.assign', name: 'did_numbers.assign', display_name: 'Назначение номеров', category: 'DID', description: 'Назначение номеров клиентам' },
  
  // Пользователи
  { id: 'users.read', name: 'users.read', display_name: 'Просмотр пользователей', category: 'Пользователи', description: 'Просмотр пользователей' },
  { id: 'users.create', name: 'users.create', display_name: 'Создание пользователей', category: 'Пользователи', description: 'Создание пользователей' },
  { id: 'users.update', name: 'users.update', display_name: 'Редактирование пользователей', category: 'Пользователи', description: 'Редактирование пользователей' },
  { id: 'users.delete', name: 'users.delete', display_name: 'Удаление пользователей', category: 'Пользователи', description: 'Удаление пользователей' },
  
  // Тикеты
  { id: 'tickets.read', name: 'tickets.read', display_name: 'Просмотр тикетов', category: 'Поддержка', description: 'Просмотр тикетов' },
  { id: 'tickets.create', name: 'tickets.create', display_name: 'Создание тикетов', category: 'Поддержка', description: 'Создание тикетов' },
  { id: 'tickets.update', name: 'tickets.update', display_name: 'Обновление тикетов', category: 'Поддержка', description: 'Обновление тикетов' },
  { id: 'tickets.delete', name: 'tickets.delete', display_name: 'Удаление тикетов', category: 'Поддержка', description: 'Удаление тикетов' },
  
  // Биллинг
  { id: 'billing.read', name: 'billing.read', display_name: 'Просмотр биллинга', category: 'Биллинг', description: 'Просмотр счетов и платежей' },
  { id: 'billing.create', name: 'billing.create', display_name: 'Создание счетов', category: 'Биллинг', description: 'Создание счетов' },
  { id: 'billing.update', name: 'billing.update', display_name: 'Обновление биллинга', category: 'Биллинг', description: 'Обновление счетов и платежей' },
  
  // Отчеты
  { id: 'reports.read', name: 'reports.read', display_name: 'Просмотр отчетов', category: 'Отчеты', description: 'Просмотр отчетов' },
  { id: 'reports.create', name: 'reports.create', display_name: 'Создание отчетов', category: 'Отчеты', description: 'Создание отчетов' },
  
  // Настройки
  { id: 'settings.read', name: 'settings.read', display_name: 'Просмотр настроек', category: 'Настройки', description: 'Просмотр настроек' },
  { id: 'settings.update', name: 'settings.update', display_name: 'Изменение настроек', category: 'Настройки', description: 'Изменение настроек' },
  
  // Логи
  { id: 'logs.read', name: 'logs.read', display_name: 'Просмотр логов', category: 'Логи', description: 'Просмотр логов активности' }
]

export const Roles: React.FC = () => {
  const { user } = useAuthStore()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)

  useEffect(() => {
    fetchRoles()
  }, [user])

  const fetchRoles = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setRoles(demoRoles)
      } else {
        // Здесь будет реальный запрос к Supabase
        setRoles([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredRoles = roles.filter(role => 
    role.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getRoleIcon = (roleName: string) => {
    switch (roleName) {
      case 'provider':
        return <Shield className="h-5 w-5 text-purple-600" />
      case 'reseller':
        return <Users className="h-5 w-5 text-blue-600" />
      case 'admin':
        return <Settings className="h-5 w-5 text-green-600" />
      case 'support':
        return <Eye className="h-5 w-5 text-orange-600" />
      default:
        return <Lock className="h-5 w-5 text-gray-600" />
    }
  }

  const getPermissionCount = (permissions: string[]) => {
    if (permissions.includes('*')) {
      return 'Все разрешения'
    }
    return `${permissions.length} разрешений`
  }

  const handleDeleteRole = (roleId: string) => {
    const role = roles.find(r => r.role_id === roleId)
    if (role?.is_system) {
      alert('Системные роли нельзя удалять')
      return
    }

    if (confirm('Вы уверены, что хотите удалить эту роль?')) {
      setRoles(prev => prev.filter(r => r.role_id !== roleId))
    }
  }

  const handleSaveRole = (roleData: Role) => {
    if (roleData.role_id) {
      // Редактирование существующей роли
      setRoles(prev => prev.map(role =>
        role.role_id === roleData.role_id
          ? { ...roleData, updated_at: new Date().toISOString() }
          : role
      ))
    } else {
      // Создание новой роли
      const newRole: Role = {
        ...roleData,
        role_id: `role-${Date.now()}`,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        users_count: 0
      }
      setRoles(prev => [...prev, newRole])
    }
  }

  const handleEditRole = (role: Role) => {
    setSelectedRole(role)
    setShowEditModal(true)
  }

  const handleViewRole = (role: Role) => {
    setSelectedRole(role)
    setShowDetailsModal(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Роли и разрешения</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление ролями пользователей и их разрешениями
          </p>
        </div>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="btn btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          Создать роль
        </button>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon purple">
            <Shield className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего ролей</h3>
            <p>{roles.length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon blue">
            <Lock className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Системные роли</h3>
            <p>{roles.filter(r => r.is_system).length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <Users className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Пользователей</h3>
            <p>{roles.reduce((sum, role) => sum + role.users_count, 0)}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon orange">
            <Settings className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Разрешений</h3>
            <p>{demoPermissions.length}</p>
          </div>
        </div>
      </div>

      {/* Search and Table */}
      <div className="card">
        <div className="card-header">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Поиск ролей..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>Роль</th>
                <th>Описание</th>
                <th>Разрешения</th>
                <th>Пользователи</th>
                <th>Тип</th>
                <th>Создана</th>
                <th>Действия</th>
              </tr>
            </thead>
            <tbody>
              {filteredRoles.map((role) => (
                <tr key={role.role_id}>
                  <td>
                    <div className="flex items-center">
                      {getRoleIcon(role.name)}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {role.display_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {role.name}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {role.description}
                    </div>
                  </td>
                  <td>
                    <div className="text-sm text-gray-600">
                      {getPermissionCount(role.permissions)}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-900">
                        {role.users_count}
                      </span>
                    </div>
                  </td>
                  <td>
                    <span className={`badge ${role.is_system ? 'badge-warning' : 'badge-success'}`}>
                      {role.is_system ? 'Системная' : 'Пользовательская'}
                    </span>
                  </td>
                  <td className="text-sm text-gray-500">
                    {formatDate(role.created_at)}
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleViewRole(role)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Просмотр"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                      {!role.is_system && (
                        <>
                          <button
                            onClick={() => handleEditRole(role)}
                            className="p-1 text-gray-400 hover:text-green-600"
                            title="Редактировать"
                          >
                            <Edit className="h-4 w-4" />
                          </button>

                          <button
                            onClick={() => handleDeleteRole(role.role_id)}
                            className="p-1 text-gray-400 hover:text-red-600"
                            title="Удалить"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredRoles.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm 
                  ? 'Роли не найдены по заданным критериям'
                  : 'Пока нет ролей'
                }
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Модальные окна */}
      <RoleModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSave={handleSaveRole}
        permissions={demoPermissions}
      />

      <RoleModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setSelectedRole(null)
        }}
        onSave={handleSaveRole}
        role={selectedRole}
        permissions={demoPermissions}
      />

      <RoleDetailsModal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false)
          setSelectedRole(null)
        }}
        role={selectedRole}
        permissions={demoPermissions}
      />
    </div>
  )
}
