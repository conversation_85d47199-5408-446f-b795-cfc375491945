-- Инициализация базы данных SPaaS платформы
-- Этот файл создает всю схему базы данных и настраивает RLS политики

-- Включаем необходимые расширения
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Создание схем
\echo 'Создание таблиц...'

-- Основные таблицы
\i database/schema/01_tenants.sql
\i database/schema/02_users.sql
\i database/schema/03_clients.sql
\i database/schema/04_sip_accounts.sql
\i database/schema/05_did_numbers.sql
\i database/schema/06_tickets.sql
\i database/schema/07_invoices.sql
\i database/schema/08_logs.sql

\echo 'Настройка Row Level Security...'

-- RLS политики
\i database/rls/01_enable_rls.sql
\i database/rls/02_tenants_policies.sql
\i database/rls/03_users_policies.sql
\i database/rls/04_clients_policies.sql

\echo 'Создание начальных данных...'

-- Создание первого Provider пользователя (будет создан через Supabase Auth)
-- Этот скрипт нужно запустить после создания пользователя в Supabase Auth

-- Функция для создания первого Provider
CREATE OR REPLACE FUNCTION create_initial_provider(
    provider_user_id UUID,
    provider_email TEXT,
    provider_name TEXT DEFAULT 'Platform Provider'
)
RETURNS UUID AS $$
DECLARE
    provider_tenant_id UUID;
BEGIN
    -- Создаем специальный tenant для Provider (опционально)
    INSERT INTO tenants (
        tenant_id,
        name,
        domain,
        status,
        subscription_plan,
        max_users,
        max_clients
    ) VALUES (
        gen_random_uuid(),
        'Platform Administration',
        'admin.platform.com',
        'active',
        'unlimited',
        999999,
        999999
    ) RETURNING tenant_id INTO provider_tenant_id;

    -- Создаем запись пользователя Provider
    INSERT INTO users (
        user_id,
        tenant_id,
        role,
        first_name,
        last_name,
        is_active
    ) VALUES (
        provider_user_id,
        provider_tenant_id,
        'provider',
        split_part(provider_name, ' ', 1),
        split_part(provider_name, ' ', 2),
        true
    );

    -- Логируем создание
    PERFORM log_action(
        'CREATE_INITIAL_PROVIDER',
        'users',
        provider_user_id,
        'Initial provider user created',
        jsonb_build_object('email', provider_email, 'name', provider_name)
    );

    RETURN provider_tenant_id;
END;
$$ LANGUAGE plpgsql;

-- Функция для создания демо данных
CREATE OR REPLACE FUNCTION create_demo_data()
RETURNS VOID AS $$
DECLARE
    demo_tenant_id UUID;
    demo_reseller_id UUID;
    demo_client_id UUID;
BEGIN
    -- Создаем демо арендатора
    INSERT INTO tenants (
        name,
        domain,
        status,
        subscription_plan,
        max_users,
        max_clients
    ) VALUES (
        'Demo Reseller Company',
        'demo.example.com',
        'active',
        'professional',
        50,
        500
    ) RETURNING tenant_id INTO demo_tenant_id;

    -- Создаем демо клиента
    INSERT INTO clients (
        tenant_id,
        client_code,
        company_name,
        contact_person,
        email,
        phone,
        status,
        billing_type
    ) VALUES (
        demo_tenant_id,
        'DEMO001',
        'Demo Client Corp',
        'John Doe',
        '<EMAIL>',
        '+1234567890',
        'active',
        'postpaid'
    ) RETURNING client_id INTO demo_client_id;

    -- Создаем демо DID номер
    INSERT INTO did_numbers (
        tenant_id,
        client_id,
        number,
        country_code,
        area_code,
        number_type,
        status,
        monthly_cost,
        currency
    ) VALUES (
        demo_tenant_id,
        demo_client_id,
        '+12345678901',
        '+1',
        '234',
        'local',
        'assigned',
        5.00,
        'USD'
    );

    RAISE NOTICE 'Demo data created successfully. Tenant ID: %', demo_tenant_id;
END;
$$ LANGUAGE plpgsql;

\echo 'База данных инициализирована успешно!';
