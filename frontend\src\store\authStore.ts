import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '@/lib/supabase'

export interface AppUser {
  id: string
  email: string
  role: 'provider' | 'reseller' | 'admin' | 'support' | 'staff' | 'client'
  tenant_id: string
  first_name: string | null
  last_name: string | null
  tenant: {
    tenant_id: string
    name: string
    status: string
  } | null
}

interface AuthState {
  user: AppUser | null
  isLoading: boolean
  isAuthenticated: boolean
  
  // Actions
  setUser: (user: AppUser | null) => void
  setLoading: (loading: boolean) => void
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: true,
      isAuthenticated: false,

      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user,
        isLoading: false 
      }),

      setLoading: (isLoading) => set({ isLoading }),

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        
        try {
          // Используем нашу Edge Function для аутентификации
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/auth-handler`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
            },
            body: JSON.stringify({ email, password }),
          })

          const result = await response.json()

          if (!response.ok) {
            throw new Error(result.error || 'Login failed')
          }

          // Устанавливаем сессию в Supabase клиенте
          if (result.access_token) {
            await supabase.auth.setSession({
              access_token: result.access_token,
              refresh_token: result.refresh_token,
            })
          }

          set({ 
            user: result.user, 
            isAuthenticated: true,
            isLoading: false 
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: async () => {
        await supabase.auth.signOut()
        set({ 
          user: null, 
          isAuthenticated: false,
          isLoading: false 
        })
      },

      checkAuth: async () => {
        set({ isLoading: true })
        
        try {
          const { data: { session } } = await supabase.auth.getSession()
          
          if (session?.user) {
            // Получаем дополнительную информацию о пользователе
            const { data, error } = await supabase
              .from('users')
              .select(`
                user_id,
                tenant_id,
                role,
                first_name,
                last_name,
                tenants (
                  tenant_id,
                  name,
                  status
                )
              `)
              .eq('user_id', session.user.id)
              .single()

            if (error || !data) {
              throw new Error('User profile not found')
            }

            const user: AppUser = {
              id: data.user_id,
              email: session.user.email || '',
              role: data.role,
              tenant_id: data.tenant_id,
              first_name: data.first_name,
              last_name: data.last_name,
              tenant: data.tenants
            }

            set({ 
              user, 
              isAuthenticated: true,
              isLoading: false 
            })
          } else {
            set({ 
              user: null, 
              isAuthenticated: false,
              isLoading: false 
            })
          }
        } catch (error) {
          console.error('Auth check failed:', error)
          set({ 
            user: null, 
            isAuthenticated: false,
            isLoading: false 
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)
