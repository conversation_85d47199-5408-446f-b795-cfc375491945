import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '@/lib/supabase'

export interface AppUser {
  id: string
  email: string
  role: 'provider' | 'reseller' | 'admin' | 'support' | 'staff' | 'client'
  tenant_id: string
  first_name: string | null
  last_name: string | null
  tenant: {
    tenant_id: string
    name: string
    status: string
  } | null
}

interface AuthState {
  user: AppUser | null
  isLoading: boolean
  isAuthenticated: boolean
  
  // Actions
  setUser: (user: AppUser | null) => void
  setLoading: (loading: boolean) => void
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: true,
      isAuthenticated: false,

      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user,
        isLoading: false 
      }),

      setLoading: (isLoading) => set({ isLoading }),

      login: async (email: string, password: string) => {
        set({ isLoading: true })

        try {
          // ДЕМО РЕЖИМ - используем локальную аутентификацию
          const demoUsers = {
            '<EMAIL>': {
              password: 'admin123',
              user: {
                id: 'demo-admin-id',
                email: '<EMAIL>',
                role: 'provider' as const,
                tenant_id: 'demo-tenant-id',
                first_name: 'Demo',
                last_name: 'Admin',
                tenant: {
                  tenant_id: 'demo-tenant-id',
                  name: 'Demo Company',
                  status: 'active'
                }
              }
            },
            '<EMAIL>': {
              password: 'reseller123',
              user: {
                id: 'demo-reseller-id',
                email: '<EMAIL>',
                role: 'reseller' as const,
                tenant_id: 'demo-tenant-id',
                first_name: 'Demo',
                last_name: 'Reseller',
                tenant: {
                  tenant_id: 'demo-tenant-id',
                  name: 'Demo Reseller Company',
                  status: 'active'
                }
              }
            },
            '<EMAIL>': {
              password: 'support123',
              user: {
                id: 'demo-support-id',
                email: '<EMAIL>',
                role: 'support' as const,
                tenant_id: 'demo-tenant-id',
                first_name: 'Demo',
                last_name: 'Support',
                tenant: {
                  tenant_id: 'demo-tenant-id',
                  name: 'Demo Company',
                  status: 'active'
                }
              }
            }
          }

          const demoUser = demoUsers[email as keyof typeof demoUsers]

          if (!demoUser || demoUser.password !== password) {
            throw new Error('Неверный email или пароль')
          }

          // Симулируем задержку
          await new Promise(resolve => setTimeout(resolve, 1000))

          // Сохраняем в localStorage для демо режима
          localStorage.setItem('demo-user', JSON.stringify(demoUser.user))

          set({
            user: demoUser.user,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: async () => {
        // В демо режиме просто очищаем localStorage
        localStorage.removeItem('demo-user')
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      checkAuth: async () => {
        set({ isLoading: true })

        try {
          // В демо режиме проверяем localStorage
          const savedUser = localStorage.getItem('demo-user')
          if (savedUser) {
            const user = JSON.parse(savedUser)
            set({
              user,
              isAuthenticated: true,
              isLoading: false
            })
          } else {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false
            })
          }
        } catch (error) {
          console.error('Auth check failed:', error)
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)
