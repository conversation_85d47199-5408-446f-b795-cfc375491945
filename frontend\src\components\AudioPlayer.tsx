import React, { useRef, useEffect, useState } from 'react'
import { 
  Phone,
  PhoneOff, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Users,
  Pause,
  Play,
  RotateCcw,
  Settings
} from 'lucide-react'

interface AudioPlayerProps {
  localStream?: MediaStream
  remoteStream?: MediaStream
  isLocalMuted?: boolean
  isRemoteAudioEnabled?: boolean
  participantName?: string
  participantNumber?: string
  callDuration?: number
  callDirection?: 'inbound' | 'outbound'
  onToggleLocalMute?: () => void
  onToggleRemoteAudio?: () => void
  onEndCall?: () => void
  onHold?: () => void
  isOnHold?: boolean
  className?: string
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  localStream,
  remoteStream,
  isLocalMuted = false,
  isRemoteAudioEnabled = true,
  participantName,
  participantNumber,
  callDuration = 0,
  callDirection = 'outbound',
  onToggleLocalMute,
  onToggleRemoteAudio,
  onEndCall,
  onHold,
  isOnHold = false,
  className = ''
}) => {
  const localAudioRef = useRef<HTMLAudioElement>(null)
  const remoteAudioRef = useRef<HTMLAudioElement>(null)
  const [audioLevel, setAudioLevel] = useState(0)
  const [remoteAudioLevel, setRemoteAudioLevel] = useState(0)

  useEffect(() => {
    if (localAudioRef.current && localStream) {
      localAudioRef.current.srcObject = localStream
    }
  }, [localStream])

  useEffect(() => {
    if (remoteAudioRef.current && remoteStream) {
      remoteAudioRef.current.srcObject = remoteStream
    }
  }, [remoteStream])

  // Анализатор аудио для визуализации уровня
  useEffect(() => {
    if (!localStream) return

    const audioContext = new AudioContext()
    const analyser = audioContext.createAnalyser()
    const source = audioContext.createMediaStreamSource(localStream)
    
    source.connect(analyser)
    analyser.fftSize = 256
    
    const bufferLength = analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    
    const updateAudioLevel = () => {
      analyser.getByteFrequencyData(dataArray)
      const average = dataArray.reduce((a, b) => a + b) / bufferLength
      setAudioLevel(average / 255)
      requestAnimationFrame(updateAudioLevel)
    }
    
    updateAudioLevel()
    
    return () => {
      audioContext.close()
    }
  }, [localStream])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  const getStatusText = () => {
    if (isOnHold) return 'На удержании'
    if (callDirection === 'inbound') return 'Входящий звонок'
    return 'Исходящий звонок'
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-8 ${className}`}>
      {/* Hidden audio elements */}
      <audio ref={localAudioRef} muted />
      <audio ref={remoteAudioRef} autoPlay />

      {/* Call Status */}
      <div className="text-center mb-8">
        <div className="relative inline-block mb-4">
          {/* Avatar with audio visualization */}
          <div className={`w-32 h-32 rounded-full flex items-center justify-center text-white text-3xl font-bold transition-all duration-300 ${
            isOnHold ? 'bg-yellow-500' : 'bg-blue-500'
          }`} style={{
            transform: `scale(${1 + (remoteAudioLevel * 0.2)})`,
            boxShadow: `0 0 ${20 + (remoteAudioLevel * 30)}px rgba(59, 130, 246, 0.5)`
          }}>
            {participantName ? getInitials(participantName) : <Users className="h-16 w-16" />}
          </div>
          
          {/* Connection indicator */}
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Participant info */}
        <h2 className="text-2xl font-semibold text-gray-900 mb-1">
          {participantName || participantNumber || 'Неизвестный номер'}
        </h2>
        
        {participantName && (
          <p className="text-gray-600 mb-2">{participantNumber}</p>
        )}
        
        <p className="text-sm text-gray-500 mb-4">{getStatusText()}</p>

        {/* Call duration */}
        <div className="text-3xl font-mono text-gray-900 mb-6">
          {formatDuration(callDuration)}
        </div>

        {/* Audio level indicators */}
        <div className="flex justify-center space-x-8 mb-6">
          {/* Local audio level */}
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-2">Ваш микрофон</div>
            <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-100 ${
                  isLocalMuted ? 'bg-red-500' : 'bg-green-500'
                }`}
                style={{ width: `${isLocalMuted ? 0 : audioLevel * 100}%` }}
              />
            </div>
            {isLocalMuted && <MicOff className="h-4 w-4 text-red-500 mx-auto mt-1" />}
          </div>

          {/* Remote audio level */}
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-2">Собеседник</div>
            <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-100 ${
                  isRemoteAudioEnabled ? 'bg-blue-500' : 'bg-gray-400'
                }`}
                style={{ width: `${isRemoteAudioEnabled ? remoteAudioLevel * 100 : 0}%` }}
              />
            </div>
            {!isRemoteAudioEnabled && <VolumeX className="h-4 w-4 text-gray-500 mx-auto mt-1" />}
          </div>
        </div>
      </div>

      {/* Call Controls */}
      <div className="flex justify-center space-x-6">
        {/* Mute Button */}
        <button
          onClick={onToggleLocalMute}
          className={`p-4 rounded-full transition-all duration-200 ${
            isLocalMuted 
              ? 'bg-red-500 text-white shadow-lg' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          title={isLocalMuted ? 'Включить микрофон' : 'Выключить микрофон'}
        >
          {isLocalMuted ? <MicOff className="h-6 w-6" /> : <Mic className="h-6 w-6" />}
        </button>

        {/* Speaker Button */}
        <button
          onClick={onToggleRemoteAudio}
          className={`p-4 rounded-full transition-all duration-200 ${
            isRemoteAudioEnabled 
              ? 'bg-gray-100 text-gray-700 hover:bg-gray-200' 
              : 'bg-yellow-500 text-white shadow-lg'
          }`}
          title={isRemoteAudioEnabled ? 'Отключить звук' : 'Включить звук'}
        >
          {isRemoteAudioEnabled ? <Volume2 className="h-6 w-6" /> : <VolumeX className="h-6 w-6" />}
        </button>

        {/* Hold Button */}
        {onHold && (
          <button
            onClick={onHold}
            className={`p-4 rounded-full transition-all duration-200 ${
              isOnHold 
                ? 'bg-yellow-500 text-white shadow-lg' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            title={isOnHold ? 'Снять с удержания' : 'Поставить на удержание'}
          >
            {isOnHold ? <Play className="h-6 w-6" /> : <Pause className="h-6 w-6" />}
          </button>
        )}

        {/* End Call Button */}
        <button
          onClick={onEndCall}
          className="p-4 rounded-full bg-red-500 text-white hover:bg-red-600 transition-all duration-200 shadow-lg"
          title="Завершить звонок"
        >
          <PhoneOff className="h-6 w-6" />
        </button>
      </div>

      {/* Additional Info */}
      <div className="mt-8 text-center">
        <div className="flex justify-center items-center space-x-4 text-sm text-gray-500">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            HD Audio
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
            Encrypted
          </div>
        </div>
      </div>
    </div>
  )
}
