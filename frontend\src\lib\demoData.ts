// Демо данные для тестирования интерфейса

export const demoClients = [
  {
    client_id: 'demo-client-1',
    tenant_id: 'demo-tenant-id',
    client_code: 'DEMO001',
    company_name: 'ОО<PERSON> "Демо Компания"',
    contact_person: '<PERSON><PERSON><PERSON><PERSON> Петров',
    email: '<EMAIL>',
    phone: '+7 (495) 123-45-67',
    address: 'г. Москва, ул. Демонстрационная, д. 1',
    city: 'Москва',
    country: 'Россия',
    status: 'active',
    billing_type: 'postpaid',
    credit_limit: 50000,
    current_balance: 12500,
    currency: 'RUB',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    client_id: 'demo-client-2',
    tenant_id: 'demo-tenant-id',
    client_code: 'DEMO002',
    company_name: 'ИП Сидоров А.В.',
    contact_person: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Сидоров',
    email: '<EMAIL>',
    phone: '+7 (812) 987-65-43',
    address: 'г. Санкт-Петербург, пр. Невский, д. 100',
    city: 'Санкт-Петербург',
    country: 'Россия',
    status: 'active',
    billing_type: 'prepaid',
    credit_limit: 25000,
    current_balance: 8750,
    currency: 'RUB',
    created_at: '2024-01-20T14:15:00Z',
    updated_at: '2024-01-20T14:15:00Z'
  },
  {
    client_id: 'demo-client-3',
    tenant_id: 'demo-tenant-id',
    client_code: 'DEMO003',
    company_name: 'ЗАО "Технологии Связи"',
    contact_person: 'Мария Козлова',
    email: '<EMAIL>',
    phone: '+7 (383) 555-12-34',
    address: 'г. Новосибирск, ул. Красный проспект, д. 50',
    city: 'Новосибирск',
    country: 'Россия',
    status: 'suspended',
    billing_type: 'postpaid',
    credit_limit: 100000,
    current_balance: -5000,
    currency: 'RUB',
    created_at: '2024-02-01T09:00:00Z',
    updated_at: '2024-02-01T09:00:00Z'
  }
]

export const demoTickets = [
  {
    ticket_id: 'demo-ticket-1',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-1',
    ticket_number: 'T20241201-0001',
    subject: 'Проблемы с качеством звука',
    description: 'Клиент жалуется на плохое качество звука при исходящих звонках',
    status: 'open',
    priority: 'high',
    category: 'technical',
    created_at: '2024-12-01T10:30:00Z',
    due_date: '2024-12-03T18:00:00Z',
    clients: {
      company_name: 'ООО "Демо Компания"',
      contact_person: 'Иван Петров'
    },
    assigned_user: {
      first_name: 'Demo',
      last_name: 'Support'
    },
    created_user: {
      first_name: 'Demo',
      last_name: 'Admin'
    }
  },
  {
    ticket_id: 'demo-ticket-2',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-2',
    ticket_number: 'T20241201-0002',
    subject: 'Настройка нового SIP аккаунта',
    description: 'Необходимо настроить дополнительный SIP аккаунт для нового сотрудника',
    status: 'in_progress',
    priority: 'medium',
    category: 'configuration',
    created_at: '2024-12-01T14:15:00Z',
    due_date: '2024-12-02T17:00:00Z',
    clients: {
      company_name: 'ИП Сидоров А.В.',
      contact_person: 'Алексей Сидоров'
    },
    assigned_user: {
      first_name: 'Demo',
      last_name: 'Support'
    },
    created_user: {
      first_name: 'Demo',
      last_name: 'Reseller'
    }
  },
  {
    ticket_id: 'demo-ticket-3',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-3',
    ticket_number: 'T20241130-0015',
    subject: 'Вопрос по биллингу',
    description: 'Клиент просит разъяснить позиции в последнем счете',
    status: 'resolved',
    priority: 'low',
    category: 'billing',
    created_at: '2024-11-30T16:45:00Z',
    due_date: '2024-12-01T18:00:00Z',
    clients: {
      company_name: 'ЗАО "Технологии Связи"',
      contact_person: 'Мария Козлова'
    },
    assigned_user: {
      first_name: 'Demo',
      last_name: 'Support'
    },
    created_user: {
      first_name: 'Demo',
      last_name: 'Admin'
    }
  }
]

export const demoInvoices = [
  {
    invoice_id: 'inv-1',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-1',
    invoice_number: 'INV-2024-001',
    issue_date: '2024-12-01',
    due_date: '2024-12-15',
    status: 'sent',
    subtotal: 15000,
    tax_amount: 3000,
    total_amount: 18000,
    currency: 'RUB',
    payment_terms: 'Оплата в течение 14 дней',
    clients: {
      company_name: 'ООО "Демо Компания"',
      contact_person: 'Иван Петров'
    },
    items: [
      {
        description: 'Ежемесячная абонентская плата',
        quantity: 1,
        unit_price: 10000,
        total_price: 10000,
        service_type: 'monthly_fee'
      },
      {
        description: 'Исходящие звонки',
        quantity: 250,
        unit_price: 20,
        total_price: 5000,
        service_type: 'call_charges'
      }
    ]
  },
  {
    invoice_id: 'inv-2',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-2',
    invoice_number: 'INV-2024-002',
    issue_date: '2024-12-01',
    due_date: '2024-12-15',
    status: 'paid',
    subtotal: 8000,
    tax_amount: 1600,
    total_amount: 9600,
    currency: 'RUB',
    payment_terms: 'Оплата в течение 14 дней',
    clients: {
      company_name: 'ИП Сидоров А.В.',
      contact_person: 'Алексей Сидоров'
    },
    items: [
      {
        description: 'Ежемесячная абонентская плата',
        quantity: 1,
        unit_price: 8000,
        total_price: 8000,
        service_type: 'monthly_fee'
      }
    ]
  },
  {
    invoice_id: 'inv-3',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-3',
    invoice_number: 'INV-2024-003',
    issue_date: '2024-11-15',
    due_date: '2024-11-30',
    status: 'overdue',
    subtotal: 25000,
    tax_amount: 5000,
    total_amount: 30000,
    currency: 'RUB',
    payment_terms: 'Оплата в течение 14 дней',
    clients: {
      company_name: 'ЗАО "Технологии Связи"',
      contact_person: 'Мария Козлова'
    },
    items: [
      {
        description: 'Ежемесячная абонентская плата',
        quantity: 1,
        unit_price: 15000,
        total_price: 15000,
        service_type: 'monthly_fee'
      },
      {
        description: 'Исходящие звонки',
        quantity: 500,
        unit_price: 20,
        total_price: 10000,
        service_type: 'call_charges'
      }
    ]
  }
]

export const demoPayments = [
  {
    payment_id: 'pay-1',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-2',
    invoice_id: 'inv-2',
    payment_number: 'PAY-2024-001',
    payment_date: '2024-12-02',
    amount: 9600,
    currency: 'RUB',
    payment_method: 'bank_transfer',
    status: 'completed',
    reference_number: 'TRF-*********',
    clients: {
      company_name: 'ИП Сидоров А.В.',
      contact_person: 'Алексей Сидоров'
    }
  },
  {
    payment_id: 'pay-2',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-1',
    invoice_id: null,
    payment_number: 'PAY-2024-002',
    payment_date: '2024-11-28',
    amount: 5000,
    currency: 'RUB',
    payment_method: 'card',
    status: 'completed',
    reference_number: 'CARD-*********',
    clients: {
      company_name: 'ООО "Демо Компания"',
      contact_person: 'Иван Петров'
    }
  }
]

export const demoDidNumbers = [
  {
    did_number_id: 'did-1',
    tenant_id: 'demo-tenant-id',
    number: '+***********',
    country_code: 'RU',
    city: 'Москва',
    status: 'assigned',
    assigned_to: 'sip-1',
    monthly_cost: 500,
    setup_cost: 0,
    provider: 'Demo Telecom',
    created_at: '2024-01-15T10:30:00Z',
    sip_accounts: {
      username: '1001',
      display_name: 'Иван Петров'
    },
    clients: {
      company_name: 'ООО "Демо Компания"',
      contact_person: 'Иван Петров'
    }
  },
  {
    did_number_id: 'did-2',
    tenant_id: 'demo-tenant-id',
    number: '+78*********',
    country_code: 'RU',
    city: 'Санкт-Петербург',
    status: 'assigned',
    assigned_to: 'sip-2',
    monthly_cost: 450,
    setup_cost: 0,
    provider: 'Demo Telecom',
    created_at: '2024-01-20T14:15:00Z',
    sip_accounts: {
      username: '1002',
      display_name: 'Алексей Сидоров'
    },
    clients: {
      company_name: 'ИП Сидоров А.В.',
      contact_person: 'Алексей Сидоров'
    }
  },
  {
    did_number_id: 'did-3',
    tenant_id: 'demo-tenant-id',
    number: '+***********',
    country_code: 'RU',
    city: 'Новосибирск',
    status: 'available',
    assigned_to: null,
    monthly_cost: 400,
    setup_cost: 1000,
    provider: 'Demo Telecom',
    created_at: '2024-02-01T09:00:00Z',
    sip_accounts: null,
    clients: null
  },
  {
    did_number_id: 'did-4',
    tenant_id: 'demo-tenant-id',
    number: '+***********',
    country_code: 'RU',
    city: 'Москва',
    status: 'available',
    assigned_to: null,
    monthly_cost: 500,
    setup_cost: 0,
    provider: 'Demo Telecom',
    created_at: '2024-02-05T11:30:00Z',
    sip_accounts: null,
    clients: null
  },
  {
    did_number_id: 'did-5',
    tenant_id: 'demo-tenant-id',
    number: '+***********',
    country_code: 'US',
    city: 'New York',
    status: 'reserved',
    assigned_to: null,
    monthly_cost: 800,
    setup_cost: 2000,
    provider: 'US Telecom',
    created_at: '2024-02-10T16:45:00Z',
    sip_accounts: null,
    clients: null
  }
]

export const demoUsers = [
  {
    user_id: 'user-1',
    tenant_id: 'demo-tenant-id',
    email: '<EMAIL>',
    role: 'provider',
    first_name: 'Demo',
    last_name: 'Admin',
    is_active: true,
    last_login_at: '2024-12-01T15:30:00Z',
    created_at: '2024-01-01T00:00:00Z',
    tenants: {
      name: 'Demo Company',
      status: 'active'
    }
  },
  {
    user_id: 'user-2',
    tenant_id: 'demo-tenant-id',
    email: '<EMAIL>',
    role: 'reseller',
    first_name: 'Demo',
    last_name: 'Reseller',
    is_active: true,
    last_login_at: '2024-11-30T14:20:00Z',
    created_at: '2024-01-15T10:00:00Z',
    tenants: {
      name: 'Demo Company',
      status: 'active'
    }
  },
  {
    user_id: 'user-3',
    tenant_id: 'demo-tenant-id',
    email: '<EMAIL>',
    role: 'support',
    first_name: 'Demo',
    last_name: 'Support',
    is_active: true,
    last_login_at: '2024-12-01T09:15:00Z',
    created_at: '2024-02-01T12:00:00Z',
    tenants: {
      name: 'Demo Company',
      status: 'active'
    }
  },
  {
    user_id: 'user-4',
    tenant_id: 'demo-tenant-id',
    email: '<EMAIL>',
    role: 'admin',
    first_name: 'Demo',
    last_name: 'Manager',
    is_active: false,
    last_login_at: '2024-11-15T18:30:00Z',
    created_at: '2024-03-01T14:30:00Z',
    tenants: {
      name: 'Demo Company',
      status: 'active'
    }
  }
]

export const demoTenants = [
  {
    tenant_id: 'demo-tenant-id',
    name: 'Demo Company',
    domain: 'demo.spaas.com',
    status: 'active',
    subscription_plan: 'enterprise',
    max_users: 100,
    max_clients: 500,
    current_users: 4,
    current_clients: 3,
    created_at: '2024-01-01T00:00:00Z',
    billing_email: '<EMAIL>',
    contact_person: 'Demo Admin',
    phone: '+7 (495) 123-45-67',
    address: 'г. Москва, ул. Демонстрационная, д. 1',
    monthly_fee: 50000,
    last_payment_date: '2024-12-01',
    next_billing_date: '2025-01-01'
  },
  {
    tenant_id: 'tenant-2',
    name: 'TechCorp Solutions',
    domain: 'techcorp.spaas.com',
    status: 'active',
    subscription_plan: 'professional',
    max_users: 50,
    max_clients: 200,
    current_users: 12,
    current_clients: 45,
    created_at: '2024-02-15T10:30:00Z',
    billing_email: '<EMAIL>',
    contact_person: 'Алексей Петров',
    phone: '+7 (812) 987-65-43',
    address: 'г. Санкт-Петербург, пр. Невский, д. 100',
    monthly_fee: 25000,
    last_payment_date: '2024-12-01',
    next_billing_date: '2025-01-01'
  },
  {
    tenant_id: 'tenant-3',
    name: 'StartupVoice',
    domain: 'startup.spaas.com',
    status: 'trial',
    subscription_plan: 'basic',
    max_users: 10,
    max_clients: 50,
    current_users: 3,
    current_clients: 8,
    created_at: '2024-11-20T14:15:00Z',
    billing_email: '<EMAIL>',
    contact_person: 'Мария Сидорова',
    phone: '+7 (383) 555-12-34',
    address: 'г. Новосибирск, ул. Красный проспект, д. 50',
    monthly_fee: 10000,
    last_payment_date: null,
    next_billing_date: '2024-12-20'
  },
  {
    tenant_id: 'tenant-4',
    name: 'Global Communications',
    domain: 'global.spaas.com',
    status: 'suspended',
    subscription_plan: 'enterprise',
    max_users: 200,
    max_clients: 1000,
    current_users: 45,
    current_clients: 156,
    created_at: '2023-08-10T09:00:00Z',
    billing_email: '<EMAIL>',
    contact_person: 'Иван Козлов',
    phone: '+7 (495) 777-88-99',
    address: 'г. Москва, ул. Тверская, д. 25',
    monthly_fee: 75000,
    last_payment_date: '2024-10-01',
    next_billing_date: '2024-11-01'
  }
]

export const demoActivityLogs = [
  {
    log_id: 'log-1',
    tenant_id: 'demo-tenant-id',
    user_id: 'user-1',
    action: 'LOGIN',
    table_name: 'auth',
    record_id: 'user-1',
    changes: { email: '<EMAIL>', login_time: '2024-12-01T15:30:00Z' },
    ip_address: '*************',
    created_at: '2024-12-01T15:30:00Z',
    users: { first_name: 'Demo', last_name: 'Admin' }
  },
  {
    log_id: 'log-2',
    tenant_id: 'demo-tenant-id',
    user_id: 'user-1',
    action: 'CREATE',
    table_name: 'clients',
    record_id: 'demo-client-1',
    changes: { company_name: 'ООО "Демо Компания"', contact_person: 'Иван Петров' },
    ip_address: '*************',
    created_at: '2024-12-01T14:20:00Z',
    users: { first_name: 'Demo', last_name: 'Admin' }
  },
  {
    log_id: 'log-3',
    tenant_id: 'demo-tenant-id',
    user_id: 'user-3',
    action: 'UPDATE',
    table_name: 'tickets',
    record_id: 'demo-ticket-1',
    changes: {
      old: { status: 'open' },
      new: { status: 'in_progress' }
    },
    ip_address: '*************',
    created_at: '2024-12-01T13:45:00Z',
    users: { first_name: 'Demo', last_name: 'Support' }
  },
  {
    log_id: 'log-4',
    tenant_id: 'demo-tenant-id',
    user_id: 'user-2',
    action: 'CREATE',
    table_name: 'invoices',
    record_id: 'inv-1',
    changes: { invoice_number: 'INV-2024-001', total_amount: 18000 },
    ip_address: '*************',
    created_at: '2024-12-01T12:30:00Z',
    users: { first_name: 'Demo', last_name: 'Reseller' }
  },
  {
    log_id: 'log-5',
    tenant_id: 'demo-tenant-id',
    user_id: null,
    action: 'SECURITY_ALERT',
    table_name: 'auth',
    record_id: null,
    changes: {
      type: 'multiple_failed_logins',
      ip_address: '*************',
      attempts: 5
    },
    ip_address: '*************',
    created_at: '2024-12-01T11:15:00Z',
    users: null
  },
  {
    log_id: 'log-6',
    tenant_id: 'demo-tenant-id',
    user_id: 'user-1',
    action: 'DELETE',
    table_name: 'sip_accounts',
    record_id: 'sip-old-1',
    changes: { username: '1000', display_name: 'Старый аккаунт' },
    ip_address: '*************',
    created_at: '2024-12-01T10:00:00Z',
    users: { first_name: 'Demo', last_name: 'Admin' }
  }
]

export const demoStats = {
  totalClients: demoClients.length,
  activeSipAccounts: 15,
  assignedDidNumbers: demoDidNumbers.filter(d => d.status === 'assigned').length,
  availableDidNumbers: demoDidNumbers.filter(d => d.status === 'available').length,
  openTickets: demoTickets.filter(t => t.status === 'open').length,
  monthlyRevenue: 125000,
  clientsGrowth: 12.5,
  ticketsResolved: 89,
  systemUptime: 99.9,
  // Биллинг статистика
  totalInvoices: demoInvoices.length,
  paidInvoices: demoInvoices.filter(i => i.status === 'paid').length,
  overdueInvoices: demoInvoices.filter(i => i.status === 'overdue').length,
  totalRevenue: demoInvoices.reduce((sum, inv) => sum + inv.total_amount, 0),
  outstandingAmount: demoInvoices.filter(i => i.status !== 'paid').reduce((sum, inv) => sum + inv.total_amount, 0),
  // Пользователи статистика
  totalUsers: demoUsers.length,
  activeUsers: demoUsers.filter(u => u.is_active).length,
  inactiveUsers: demoUsers.filter(u => !u.is_active).length,
  // Арендаторы статистика
  totalTenants: demoTenants.length,
  activeTenants: demoTenants.filter(t => t.status === 'active').length,
  trialTenants: demoTenants.filter(t => t.status === 'trial').length,
  suspendedTenants: demoTenants.filter(t => t.status === 'suspended').length
}
