// Демо данные для тестирования интерфейса

export const demoClients = [
  {
    client_id: 'demo-client-1',
    tenant_id: 'demo-tenant-id',
    client_code: 'DEMO001',
    company_name: 'ОО<PERSON> "Демо Компания"',
    contact_person: '<PERSON><PERSON><PERSON><PERSON> Петров',
    email: '<EMAIL>',
    phone: '+7 (495) 123-45-67',
    address: 'г. Москва, ул. Демонстрационная, д. 1',
    city: 'Москва',
    country: 'Россия',
    status: 'active',
    billing_type: 'postpaid',
    credit_limit: 50000,
    current_balance: 12500,
    currency: 'RUB',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    client_id: 'demo-client-2',
    tenant_id: 'demo-tenant-id',
    client_code: 'DEMO002',
    company_name: 'ИП Сидоров А.В.',
    contact_person: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Сидоров',
    email: '<EMAIL>',
    phone: '+7 (812) 987-65-43',
    address: 'г. Санкт-Петербург, пр. Невский, д. 100',
    city: 'Санкт-Петербург',
    country: 'Россия',
    status: 'active',
    billing_type: 'prepaid',
    credit_limit: 25000,
    current_balance: 8750,
    currency: 'RUB',
    created_at: '2024-01-20T14:15:00Z',
    updated_at: '2024-01-20T14:15:00Z'
  },
  {
    client_id: 'demo-client-3',
    tenant_id: 'demo-tenant-id',
    client_code: 'DEMO003',
    company_name: 'ЗАО "Технологии Связи"',
    contact_person: 'Мария Козлова',
    email: '<EMAIL>',
    phone: '+7 (383) 555-12-34',
    address: 'г. Новосибирск, ул. Красный проспект, д. 50',
    city: 'Новосибирск',
    country: 'Россия',
    status: 'suspended',
    billing_type: 'postpaid',
    credit_limit: 100000,
    current_balance: -5000,
    currency: 'RUB',
    created_at: '2024-02-01T09:00:00Z',
    updated_at: '2024-02-01T09:00:00Z'
  }
]

export const demoTickets = [
  {
    ticket_id: 'demo-ticket-1',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-1',
    ticket_number: 'T20241201-0001',
    subject: 'Проблемы с качеством звука',
    description: 'Клиент жалуется на плохое качество звука при исходящих звонках',
    status: 'open',
    priority: 'high',
    category: 'technical',
    created_at: '2024-12-01T10:30:00Z',
    due_date: '2024-12-03T18:00:00Z',
    clients: {
      company_name: 'ООО "Демо Компания"',
      contact_person: 'Иван Петров'
    },
    assigned_user: {
      first_name: 'Demo',
      last_name: 'Support'
    },
    created_user: {
      first_name: 'Demo',
      last_name: 'Admin'
    }
  },
  {
    ticket_id: 'demo-ticket-2',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-2',
    ticket_number: 'T20241201-0002',
    subject: 'Настройка нового SIP аккаунта',
    description: 'Необходимо настроить дополнительный SIP аккаунт для нового сотрудника',
    status: 'in_progress',
    priority: 'medium',
    category: 'configuration',
    created_at: '2024-12-01T14:15:00Z',
    due_date: '2024-12-02T17:00:00Z',
    clients: {
      company_name: 'ИП Сидоров А.В.',
      contact_person: 'Алексей Сидоров'
    },
    assigned_user: {
      first_name: 'Demo',
      last_name: 'Support'
    },
    created_user: {
      first_name: 'Demo',
      last_name: 'Reseller'
    }
  },
  {
    ticket_id: 'demo-ticket-3',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-3',
    ticket_number: 'T20241130-0015',
    subject: 'Вопрос по биллингу',
    description: 'Клиент просит разъяснить позиции в последнем счете',
    status: 'resolved',
    priority: 'low',
    category: 'billing',
    created_at: '2024-11-30T16:45:00Z',
    due_date: '2024-12-01T18:00:00Z',
    clients: {
      company_name: 'ЗАО "Технологии Связи"',
      contact_person: 'Мария Козлова'
    },
    assigned_user: {
      first_name: 'Demo',
      last_name: 'Support'
    },
    created_user: {
      first_name: 'Demo',
      last_name: 'Admin'
    }
  }
]

export const demoStats = {
  totalClients: demoClients.length,
  activeSipAccounts: 15,
  assignedDidNumbers: 8,
  openTickets: demoTickets.filter(t => t.status === 'open').length,
  monthlyRevenue: 125000,
  clientsGrowth: 12.5,
  ticketsResolved: 89,
  systemUptime: 99.9
}
