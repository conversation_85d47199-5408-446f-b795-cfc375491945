# Развертывание SPaaS Platform

## 🚀 Полное руководство по развертыванию

### Предварительные требования

- Node.js 18+
- Аккаунт в Supabase
- Git

### 1. Подготовка проекта

```bash
# Клонируйте или скопируйте проект
git clone <your-repo-url>
cd spaas-platform

# Запустите автоматическую настройку
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Настройка Supabase

#### 2.1 Создание проекта
1. Перейдите на [supabase.com](https://supabase.com)
2. Создайте новый проект
3. Дождитесь завершения инициализации

#### 2.2 Получение ключей
1. Перейдите в Settings → API
2. Скопируйте:
   - Project URL
   - anon public key
   - service_role key (для Edge Functions)

#### 2.3 Настройка переменных окружения
Отредактируйте `frontend/.env`:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
VITE_APP_NAME=SPaaS Platform
VITE_APP_ENVIRONMENT=production
```

### 3. Настройка базы данных

#### 3.1 Выполнение миграций
1. Откройте Supabase Studio → SQL Editor
2. Скопируйте весь код из `scripts/migrate.sql`
3. Выполните скрипт
4. Убедитесь, что все таблицы созданы без ошибок

#### 3.2 Проверка RLS политик
В SQL Editor выполните:
```sql
-- Проверка включения RLS
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;

-- Проверка политик
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

### 4. Создание первого пользователя

#### 4.1 Создание в Supabase Auth
1. Перейдите в Authentication → Users
2. Нажмите "Add user"
3. Заполните:
   - Email: <EMAIL>
   - Password: (надежный пароль)
   - Email Confirm: true

#### 4.2 Создание профиля Provider
В SQL Editor выполните:
```sql
-- Замените USER_ID на реальный ID из auth.users
INSERT INTO tenants (name, domain, status, subscription_plan, max_users, max_clients)
VALUES ('Platform Administration', 'admin.platform.com', 'active', 'unlimited', 999999, 999999);

-- Получите tenant_id из предыдущего запроса и замените в следующем
INSERT INTO users (user_id, tenant_id, role, first_name, last_name, is_active)
VALUES (
    'USER_ID_FROM_AUTH_USERS',  -- замените на реальный ID
    'TENANT_ID_FROM_PREVIOUS_QUERY',  -- замените на реальный tenant_id
    'provider',
    'Platform',
    'Administrator',
    true
);
```

### 5. Создание демо данных (опционально)

```sql
SELECT create_demo_data();
```

### 6. Настройка Edge Functions

#### 6.1 Установка Supabase CLI
```bash
npm install -g supabase
```

#### 6.2 Логин и связывание проекта
```bash
supabase login
supabase link --project-ref your-project-id
```

#### 6.3 Развертывание функций
```bash
supabase functions deploy auth-handler
```

### 7. Запуск приложения

#### 7.1 Разработка
```bash
cd frontend
npm install
npm run dev
```

#### 7.2 Продакшн сборка
```bash
cd frontend
npm run build
```

### 8. Проверка развертывания

#### 8.1 Тест аутентификации
1. Откройте приложение
2. Войдите с созданными учетными данными
3. Проверьте отображение дашборда

#### 8.2 Тест RLS
1. Создайте второго пользователя с ролью 'reseller'
2. Убедитесь, что он видит только свои данные
3. Проверьте, что Provider видит все данные

#### 8.3 Тест функциональности
- Создание клиентов
- Создание тикетов
- Просмотр логов
- Изменение настроек

### 9. Настройка продакшн среды

#### 9.1 Домен и SSL
1. Настройте кастомный домен в Supabase
2. Обновите CORS настройки
3. Настройте SSL сертификаты

#### 9.2 Мониторинг
1. Настройте алерты в Supabase
2. Подключите внешний мониторинг
3. Настройте бэкапы

#### 9.3 Безопасность
```sql
-- Отключите регистрацию через API
UPDATE auth.config SET enable_signup = false;

-- Настройте JWT expiry
UPDATE auth.config SET jwt_expiry_limit = 3600;
```

### 10. Устранение проблем

#### Ошибка "User profile not found"
```sql
-- Проверьте связь между auth.users и users
SELECT au.id, au.email, u.user_id, u.role 
FROM auth.users au 
LEFT JOIN users u ON au.id = u.user_id;
```

#### Ошибки RLS
```sql
-- Проверьте JWT claims
SELECT auth.jwt();

-- Проверьте функции RLS
SELECT auth.user_id(), auth.user_role(), auth.user_tenant_id();
```

#### Проблемы с Edge Functions
```bash
# Проверьте логи функций
supabase functions logs auth-handler

# Пересоздайте функцию
supabase functions delete auth-handler
supabase functions deploy auth-handler
```

### 11. Обновления

#### Обновление схемы БД
1. Создайте новый SQL файл в `database/migrations/`
2. Выполните миграцию в Supabase Studio
3. Обновите типы в `frontend/src/lib/supabase.ts`

#### Обновление фронтенда
```bash
cd frontend
npm run build
# Разверните на вашем хостинге
```

### 12. Поддержка

- Документация: `docs/`
- Логи: Supabase Studio → Logs
- Мониторинг: Supabase Studio → Reports
- Бэкапы: Supabase Studio → Settings → Database

## 🎯 Чек-лист развертывания

- [ ] Supabase проект создан
- [ ] Переменные окружения настроены
- [ ] База данных мигрирована
- [ ] RLS политики активны
- [ ] Первый пользователь создан
- [ ] Edge Functions развернуты
- [ ] Приложение запускается
- [ ] Аутентификация работает
- [ ] Данные отображаются корректно
- [ ] Роли и права работают
- [ ] Логирование активно
- [ ] Продакшн настройки применены
