-- Таблица DID номеров
CREATE TABLE IF NOT EXISTS did_numbers (
    did_number_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(client_id) ON DELETE SET NULL,
    number VARCHAR(20) NOT NULL UNIQUE,
    country_code VARCHAR(5),
    area_code VARCHAR(10),
    number_type VARCHAR(50) DEFAULT 'local' CHECK (number_type IN ('local', 'national', 'international', 'toll_free')),
    status VARCHAR(50) DEFAULT 'available' CHECK (status IN ('available', 'assigned', 'suspended', 'ported_out')),
    monthly_cost DECIMAL(8,2) DEFAULT 0,
    setup_cost DECIMAL(8,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    provider VARCHAR(100),
    provider_id VARCHAR(100),
    routing_settings JSONB DEFAULT '{}',
    features JSONB DEFAULT '{}', -- SMS, MMS, Voice, Fax capabilities
    assigned_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Индексы
CREATE INDEX IF NOT EXISTS idx_did_numbers_tenant_id ON did_numbers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_did_numbers_client_id ON did_numbers(client_id);
CREATE INDEX IF NOT EXISTS idx_did_numbers_number ON did_numbers(number);
CREATE INDEX IF NOT EXISTS idx_did_numbers_status ON did_numbers(status);
CREATE INDEX IF NOT EXISTS idx_did_numbers_country_code ON did_numbers(country_code);
CREATE INDEX IF NOT EXISTS idx_did_numbers_number_type ON did_numbers(number_type);
CREATE INDEX IF NOT EXISTS idx_did_numbers_provider ON did_numbers(provider);
CREATE INDEX IF NOT EXISTS idx_did_numbers_expires_at ON did_numbers(expires_at);

-- Триггер для обновления updated_at
CREATE TRIGGER update_did_numbers_updated_at 
    BEFORE UPDATE ON did_numbers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Триггер для автоматического обновления assigned_at при назначении клиенту
CREATE OR REPLACE FUNCTION update_did_assigned_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Если номер назначается клиенту
    IF NEW.client_id IS NOT NULL AND OLD.client_id IS NULL THEN
        NEW.assigned_at = NOW();
        NEW.status = 'assigned';
    -- Если номер освобождается
    ELSIF NEW.client_id IS NULL AND OLD.client_id IS NOT NULL THEN
        NEW.assigned_at = NULL;
        NEW.status = 'available';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_did_assigned_at_trigger
    BEFORE UPDATE ON did_numbers
    FOR EACH ROW
    EXECUTE FUNCTION update_did_assigned_at();

-- Комментарии
COMMENT ON TABLE did_numbers IS 'DID номера (прямые входящие номера)';
COMMENT ON COLUMN did_numbers.number IS 'Номер телефона в международном формате';
COMMENT ON COLUMN did_numbers.number_type IS 'Тип номера: local, national, international, toll_free';
COMMENT ON COLUMN did_numbers.status IS 'Статус номера: available, assigned, suspended, ported_out';
COMMENT ON COLUMN did_numbers.monthly_cost IS 'Ежемесячная стоимость номера';
COMMENT ON COLUMN did_numbers.setup_cost IS 'Стоимость подключения номера';
COMMENT ON COLUMN did_numbers.provider IS 'Поставщик номера';
COMMENT ON COLUMN did_numbers.routing_settings IS 'Настройки маршрутизации в формате JSON';
COMMENT ON COLUMN did_numbers.features IS 'Возможности номера (SMS, MMS, Voice, Fax) в формате JSON';
