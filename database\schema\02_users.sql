-- Расширение таблицы пользователей (связь с auth.users)
CREATE TABLE IF NOT EXISTS users (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL CHECK (role IN ('provider', 'reseller', 'admin', 'support', 'staff', 'client')),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    position VARCHAR(100),
    department VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    settings JSONB DEFAULT '{}',
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Индексы
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_tenant_role ON users(tenant_id, role);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_at);

-- Триггер для обновления updated_at
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Ограничения
-- Provider может быть только один на всю платформу (без tenant_id)
-- Reseller может быть только один на tenant
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_provider 
    ON users(role) 
    WHERE role = 'provider';

CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_reseller_per_tenant 
    ON users(tenant_id, role) 
    WHERE role = 'reseller';

-- Комментарии
COMMENT ON TABLE users IS 'Пользователи системы (расширение auth.users)';
COMMENT ON COLUMN users.user_id IS 'Ссылка на auth.users.id';
COMMENT ON COLUMN users.tenant_id IS 'Арендатор, к которому принадлежит пользователь';
COMMENT ON COLUMN users.role IS 'Роль пользователя в системе';
COMMENT ON COLUMN users.permissions IS 'Дополнительные разрешения в формате JSON';
COMMENT ON COLUMN users.settings IS 'Пользовательские настройки в формате JSON';
