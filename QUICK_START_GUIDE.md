# 🚀 Краткое руководство по новым функциям

## 🎯 Обзор новых возможностей

В SPaaS платформе добавлены три ключевые системы:

1. **🛒 Система покупки DID номеров** - полное управление покупками номеров
2. **🔐 Система управления ролями** - создание и настройка ролей пользователей  
3. **🔌 Система интеграций** - подключение внешних сервисов

## 📱 Навигация

Новые разделы добавлены в боковое меню:

```
📊 Дашборд
👥 Клиенты
👤 Пользователи
🛡️ Роли              ← НОВОЕ
📞 SIP Аккаунты
📱 DID Номера         ← ОБНОВЛЕНО
🎫 Тикеты
💰 Биллинг
📊 Отчеты
📋 Логи
⚡ Интеграции         ← НОВОЕ
⚙️ Настройки
```

## 🛒 Покупка DID номеров

### Как купить номера:
1. Перейдите в **"DID Номера"**
2. Нажмите **"Купить номера"**
3. Выберите нужные номера из каталога
4. Используйте фильтры по странам и типам
5. Добавьте примечания к покупке
6. Подтвердите покупку

### Просмотр истории покупок:
1. На странице DID номеров нажмите **"История покупок"**
2. Используйте фильтры для поиска
3. Экспортируйте данные в CSV при необходимости

### Информация о покупках:
- В таблице номеров добавлена колонка **"Покупка"**
- Показывает кто, когда и с какими примечаниями купил номер

## 🔐 Управление ролями

### Просмотр ролей:
1. Перейдите в **"Роли"**
2. Изучите системные роли (Провайдер, Реселлер, Администратор, Поддержка)
3. Просмотрите пользовательские роли

### Создание новой роли:
1. Нажмите **"Создать роль"**
2. Заполните системное имя (например: `manager`)
3. Укажите отображаемое имя (например: `Менеджер`)
4. Добавьте описание роли
5. Выберите разрешения по категориям:
   - 👥 Клиенты
   - 📞 SIP
   - 📱 DID
   - 👤 Пользователи
   - 🎫 Поддержка
   - 💰 Биллинг
   - 📊 Отчеты
   - ⚙️ Настройки
   - 📋 Логи
6. Сохраните роль

### Редактирование роли:
1. Найдите роль в списке
2. Нажмите иконку **"Редактировать"** (только для пользовательских ролей)
3. Измените настройки
4. Сохраните изменения

### Просмотр деталей роли:
1. Нажмите иконку **"Просмотр"** (глаз)
2. Изучите все разрешения роли
3. Посмотрите статистику использования

## 🔌 Интеграции

### Просмотр интеграций:
1. Перейдите в **"Интеграции"**
2. Изучите доступные сервисы:
   - **📞 Телефония**: Twilio, SignalWire
   - **💬 Мессенджеры**: Viber, Telegram
   - **💳 Платежи**: Stripe, Square
3. Проверьте статусы подключений

### Подключение сервиса:
1. Найдите нужный сервис
2. Нажмите **"Подключить"**
3. Заполните параметры подключения:
   - API ключи
   - Токены авторизации
   - URL вебхуков
4. Протестируйте подключение
5. Сохраните настройки

### Настройка существующей интеграции:
1. Найдите подключенный сервис
2. Нажмите **"Настроить"**
3. Измените параметры
4. Протестируйте изменения
5. Сохраните настройки

### Отключение интеграции:
1. Найдите интеграцию
2. Нажмите **"Отключить"**
3. Подтвердите действие

## 📊 Дашборд

На главной странице добавлен виджет **"Интеграции"**:
- Показывает статистику подключений
- Отображает статусы всех интеграций
- Быстрый переход к управлению

## 🔒 Права доступа

### Роли и доступ к новым функциям:

#### 🛒 Покупка DID номеров:
- **Все роли** могут просматривать историю
- **Provider, Admin** могут покупать номера

#### 🔐 Управление ролями:
- **Provider, Admin** - полный доступ
- Системные роли защищены от изменений

#### 🔌 Интеграции:
- **Provider, Admin** - полный доступ
- Конфиденциальные данные маскируются

## ⚡ Быстрые действия

### На дашборде:
- **Добавить клиента** - переход к созданию клиента
- **Создать тикет** - переход к созданию тикета  
- **Назначить номер** - переход к DID номерам
- **Просмотреть отчеты** - переход к отчетам

### В разделах:
- **DID Номера**: Купить номера, История покупок
- **Роли**: Создать роль, Просмотр деталей
- **Интеграции**: Подключить сервис, Настроить

## 🎨 Интерфейс

### Цветовая индикация:
- **🟢 Зеленый** - успешные операции, подключенные сервисы
- **🔴 Красный** - ошибки, проблемы
- **🟡 Желтый** - предупреждения, ожидание
- **🔵 Синий** - информация, активные элементы
- **⚪ Серый** - отключенные, неактивные элементы

### Иконки:
- **🛡️** - роли и безопасность
- **⚡** - интеграции и подключения
- **🛒** - покупки и транзакции
- **👁️** - просмотр деталей
- **⚙️** - настройки и конфигурация

## 🔧 Техническая информация

### Демо режим:
- Все функции работают с демо данными
- Пользователи с `@demo.com` видят тестовые данные
- Реальные API вызовы заменены симуляцией

### Безопасность:
- Токены и пароли маскируются в интерфейсе
- Системные роли защищены от изменений
- Валидация данных на всех уровнях

### Производительность:
- Ленивая загрузка компонентов
- Кэширование данных
- Оптимизированные запросы

## 📱 Мобильная версия

Все новые функции адаптированы для мобильных устройств:
- Адаптивные таблицы
- Мобильные модальные окна
- Сенсорное управление

## 🆘 Поддержка

При возникновении проблем:
1. Проверьте права доступа к функции
2. Убедитесь в корректности введенных данных
3. Обратитесь к документации API сервисов
4. Создайте тикет в системе поддержки

## 🔮 Планы развития

В следующих версиях планируется:
- Автоматизация процессов
- Расширенная аналитика
- Дополнительные интеграции
- Улучшенная система уведомлений
