import React, { useEffect, useState } from 'react'
import { Plus, Search, Filter, Phone, PhoneCall, PhoneOff, Edit, Trash2 } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate, getStatusColor } from '@/lib/utils'
import { SipAccountForm } from '@/components/SipAccountForm'
import { demoClients } from '@/lib/demoData'

interface SipAccount {
  sip_account_id: string
  tenant_id: string
  client_id: string
  username: string
  domain: string
  display_name: string
  status: string
  max_concurrent_calls: number
  last_registration: string | null
  registration_expires: string | null
  user_agent: string | null
  ip_address: string | null
  clients?: {
    company_name: string
    contact_person: string
  }
}

// Демо данные для SIP аккаунтов
const demoSipAccounts: SipAccount[] = [
  {
    sip_account_id: 'sip-1',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-1',
    username: '1001',
    domain: 'sip.demo.com',
    display_name: '<PERSON>ван Петров',
    status: 'active',
    max_concurrent_calls: 2,
    last_registration: '2024-12-01T15:30:00Z',
    registration_expires: '2024-12-01T16:30:00Z',
    user_agent: 'Grandstream GXP2170',
    ip_address: '*************',
    clients: {
      company_name: 'ООО "Демо Компания"',
      contact_person: 'Иван Петров'
    }
  },
  {
    sip_account_id: 'sip-2',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-2',
    username: '1002',
    domain: 'sip.demo.com',
    display_name: 'Алексей Сидоров',
    status: 'active',
    max_concurrent_calls: 1,
    last_registration: '2024-12-01T14:45:00Z',
    registration_expires: '2024-12-01T15:45:00Z',
    user_agent: 'Yealink T46S',
    ip_address: '*************',
    clients: {
      company_name: 'ИП Сидоров А.В.',
      contact_person: 'Алексей Сидоров'
    }
  },
  {
    sip_account_id: 'sip-3',
    tenant_id: 'demo-tenant-id',
    client_id: 'demo-client-3',
    username: '1003',
    domain: 'sip.demo.com',
    display_name: 'Мария Козлова',
    status: 'inactive',
    max_concurrent_calls: 3,
    last_registration: null,
    registration_expires: null,
    user_agent: null,
    ip_address: null,
    clients: {
      company_name: 'ЗАО "Технологии Связи"',
      contact_person: 'Мария Козлова'
    }
  }
]

export const SipAccounts: React.FC = () => {
  const { user } = useAuthStore()
  const [sipAccounts, setSipAccounts] = useState<SipAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingAccount, setEditingAccount] = useState<SipAccount | null>(null)

  useEffect(() => {
    fetchSipAccounts()
  }, [user])

  const fetchSipAccounts = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setSipAccounts(demoSipAccounts)
      } else {
        // Здесь будет реальный запрос к Supabase
        setSipAccounts([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredSipAccounts = sipAccounts.filter(account => {
    const matchesSearch = 
      account.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.clients?.company_name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || account.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleCreateAccount = async (data: any) => {
    // В демо режиме просто добавляем в локальный массив
    const newAccount: SipAccount = {
      sip_account_id: `sip-${Date.now()}`,
      tenant_id: user?.tenant_id || '',
      client_id: data.client_id,
      username: data.username,
      domain: 'sip.demo.com',
      display_name: data.display_name,
      status: data.status,
      max_concurrent_calls: data.max_concurrent_calls,
      last_registration: null,
      registration_expires: null,
      user_agent: null,
      ip_address: null,
      clients: demoClients.find(c => c.client_id === data.client_id)
    }

    setSipAccounts(prev => [newAccount, ...prev])
  }

  const handleEditAccount = async (data: any) => {
    if (!editingAccount) return

    // В демо режиме обновляем локальный массив
    setSipAccounts(prev => prev.map(account =>
      account.sip_account_id === editingAccount.sip_account_id
        ? { ...account, ...data }
        : account
    ))
    setEditingAccount(null)
  }

  const handleDeleteAccount = (accountId: string) => {
    if (confirm('Вы уверены, что хотите удалить этот SIP аккаунт?')) {
      setSipAccounts(prev => prev.filter(account => account.sip_account_id !== accountId))
    }
  }

  const getRegistrationStatus = (account: SipAccount) => {
    if (!account.last_registration) return 'Не зарегистрирован'
    
    const expires = new Date(account.registration_expires || '')
    const now = new Date()
    
    if (expires > now) {
      return 'Зарегистрирован'
    } else {
      return 'Истек срок регистрации'
    }
  }

  const getRegistrationIcon = (account: SipAccount) => {
    const status = getRegistrationStatus(account)
    
    if (status === 'Зарегистрирован') {
      return <PhoneCall className="h-4 w-4 text-green-600" />
    } else if (status === 'Истек срок регистрации') {
      return <PhoneOff className="h-4 w-4 text-yellow-600" />
    } else {
      return <Phone className="h-4 w-4 text-gray-400" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">SIP Аккаунты</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление SIP аккаунтами клиентов
          </p>
        </div>
        <button
          className="btn btn-primary"
          onClick={() => setShowCreateForm(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Добавить аккаунт
        </button>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon green">
            <PhoneCall className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Активные</h3>
            <p>{sipAccounts.filter(a => a.status === 'active').length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon blue">
            <Phone className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Зарегистрированные</h3>
            <p>{sipAccounts.filter(a => getRegistrationStatus(a) === 'Зарегистрирован').length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <PhoneOff className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Неактивные</h3>
            <p>{sipAccounts.filter(a => a.status === 'inactive').length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <Phone className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего аккаунтов</h3>
            <p>{sipAccounts.length}</p>
          </div>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="card">
        <div className="card-header">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Поиск SIP аккаунтов..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все статусы</option>
                <option value="active">Активные</option>
                <option value="inactive">Неактивные</option>
                <option value="suspended">Приостановленные</option>
              </select>
            </div>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>SIP Аккаунт</th>
                <th>Клиент</th>
                <th>Статус</th>
                <th>Регистрация</th>
                <th>Устройство</th>
                <th>IP Адрес</th>
                <th>Действия</th>
              </tr>
            </thead>
            <tbody>
              {filteredSipAccounts.map((account) => (
                <tr key={account.sip_account_id}>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">
                        {account.username}@{account.domain}
                      </div>
                      <div className="text-sm text-gray-500">
                        {account.display_name}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <div className="text-sm text-gray-900">
                        {account.clients?.company_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {account.clients?.contact_person}
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className={`badge ${account.status}`}>
                      {account.status}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getRegistrationIcon(account)}
                      <span className="ml-2 text-sm">
                        {getRegistrationStatus(account)}
                      </span>
                    </div>
                    {account.registration_expires && (
                      <div className="text-xs text-gray-500">
                        Истекает: {formatDate(account.registration_expires)}
                      </div>
                    )}
                  </td>
                  <td className="text-sm text-gray-500">
                    {account.user_agent || 'Не определено'}
                  </td>
                  <td className="text-sm text-gray-500">
                    {account.ip_address || 'Не подключен'}
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setEditingAccount(account)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Редактировать"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteAccount(account.sip_account_id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Удалить"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredSipAccounts.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm || statusFilter !== 'all'
                  ? 'SIP аккаунты не найдены по заданным критериям'
                  : 'Пока нет SIP аккаунтов'
                }
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Формы */}
      <SipAccountForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSubmit={handleCreateAccount}
        clients={demoClients}
        mode="create"
      />

      <SipAccountForm
        isOpen={!!editingAccount}
        onClose={() => setEditingAccount(null)}
        onSubmit={handleEditAccount}
        clients={demoClients}
        initialData={editingAccount || undefined}
        mode="edit"
      />
    </div>
  )
}
