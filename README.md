# SPaaS Platform

Многоарендаторная платформа Software Platform as a Service на базе Supabase.

## Структура проекта

```
spaas-platform/
├── frontend/                 # Vite + React фронтенд
├── database/                 # SQL схемы и миграции
│   ├── schema/              # Основные таблицы
│   ├── rls/                 # RLS политики
│   ├── functions/           # PostgreSQL функции
│   └── migrations/          # Миграции
├── supabase/                # Supabase конфигурация
│   ├── functions/           # Edge Functions
│   ├── config/              # Конфигурация
│   └── seed/                # Тестовые данные
├── docs/                    # Документация
│   ├── architecture/        # Архитектурные диаграммы
│   ├── api/                 # API документация
│   └── deployment/          # Инструкции по развертыванию
├── scripts/                 # Утилиты и скрипты
└── README.md
```

## Технологический стек

- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Frontend**: Vite + React + TypeScript
- **Database**: PostgreSQL с Row-Level Security (RLS)
- **Auth**: JWT с кастомными claims
- **API**: PostgREST + Edge Functions

## Роли системы

- **Provider**: Глобальный администратор платформы
- **Reseller**: Владелец арендатора (tenant)
- **Admin**: Администратор арендатора
- **Support**: Поддержка арендатора
- **Staff**: Сотрудник арендатора
- **Client**: Конечный клиент

## Быстрый старт

### 1. Настройка проекта

```bash
# Запустите скрипт настройки
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Настройка Supabase

1. Создайте проект в [Supabase](https://supabase.com)
2. Скопируйте URL и anon key
3. Отредактируйте `frontend/.env`:

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 3. Настройка базы данных

В Supabase Studio → SQL Editor выполните:
```sql
-- Скопируйте содержимое scripts/migrate.sql
```

### 4. Запуск приложения

```bash
cd frontend
npm run dev
```

Откройте http://localhost:3000

## Документация

- **Быстрый старт**: [docs/QUICK_START.md](docs/QUICK_START.md)
- **Архитектура**: Подробная документация в начале этого файла
- **API**: Документация API будет добавлена в `docs/api/`

## Возможности

✅ **Реализовано:**
- **Мультиарендаторная архитектура** с полной изоляцией данных через RLS
- **Система ролей** (Provider, Reseller, Admin, Support, Staff, Client) с детальными правами доступа
- **Полная схема БД** (8 основных таблиц + связи + триггеры + функции)
- **Аутентификация** через Supabase Auth с кастомными JWT claims
- **Современный фронтенд** на React + Vite + TypeScript + Tailwind CSS
- **Навигация и роутинг** с ролевым доступом к страницам
- **Дашборд** с метриками и статистикой в реальном времени
- **Управление клиентами** - просмотр, поиск, фильтрация
- **Система тикетов** - создание, отслеживание, управление статусами
- **Настройки** - профиль, безопасность, уведомления
- **Полные RLS политики** для всех таблиц с учетом ролей
- **Система логирования** всех действий пользователей
- **Responsive дизайн** с современным UI/UX

🚧 **В разработке:**
- CRUD формы для создания/редактирования сущностей
- Управление SIP аккаунтами и DID номерами
- Биллинг модуль с инвойсами и платежами
- Расширенные отчеты и аналитика
- Система уведомлений

📋 **Планируется:**
- AI интеграции и чат-боты
- Workflow Engine для автоматизации
- Внешние интеграции (CRM, телефония, платежные системы)
- Mobile приложение
- API документация и SDK
- Тесты (unit, integration, e2e)
