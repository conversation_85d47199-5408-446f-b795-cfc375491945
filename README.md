# SPaaS Platform

Многоарендаторная платформа Software Platform as a Service на базе Supabase.

## Структура проекта

```
spaas-platform/
├── frontend/                 # Vite + React фронтенд
├── database/                 # SQL схемы и миграции
│   ├── schema/              # Основные таблицы
│   ├── rls/                 # RLS политики
│   ├── functions/           # PostgreSQL функции
│   └── migrations/          # Миграции
├── supabase/                # Supabase конфигурация
│   ├── functions/           # Edge Functions
│   ├── config/              # Конфигурация
│   └── seed/                # Тестовые данные
├── docs/                    # Документация
│   ├── architecture/        # Архитектурные диаграммы
│   ├── api/                 # API документация
│   └── deployment/          # Инструкции по развертыванию
├── scripts/                 # Утилиты и скрипты
└── README.md
```

## Технологический стек

- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Frontend**: Vite + React + TypeScript
- **Database**: PostgreSQL с Row-Level Security (RLS)
- **Auth**: JWT с кастомными claims
- **API**: PostgREST + Edge Functions

## Роли системы

- **Provider**: Глобальный администратор платформы
- **Reseller**: Владелец арендатора (tenant)
- **Admin**: Администратор арендатора
- **Support**: Поддержка арендатора
- **Staff**: Сотрудник арендатора
- **Client**: Конечный клиент

## Быстрый старт

### 1. Настройка проекта

```bash
# Запустите скрипт настройки
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Настройка Supabase

1. Создайте проект в [Supabase](https://supabase.com)
2. Скопируйте URL и anon key
3. Отредактируйте `frontend/.env`:

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 3. Настройка базы данных

В Supabase Studio → SQL Editor выполните:
```sql
-- Скопируйте содержимое scripts/migrate.sql
```

### 4. Запуск приложения

```bash
cd frontend
npm run dev
```

Откройте http://localhost:3000

## Документация

- **Быстрый старт**: [docs/QUICK_START.md](docs/QUICK_START.md)
- **Архитектура**: Подробная документация в начале этого файла
- **API**: Документация API будет добавлена в `docs/api/`

## Возможности

✅ **Реализовано:**
- Мультиарендаторная архитектура с RLS
- Система ролей и разрешений (Provider, Reseller, Admin, Support, Staff, Client)
- Базовая схема БД (tenants, users, clients, sip_accounts, did_numbers, tickets, invoices, logs)
- Аутентификация через Supabase Auth с кастомными claims
- Базовый фронтенд на React + Vite + TypeScript
- Row-Level Security политики
- Система логирования действий

🚧 **В разработке:**
- Полный CRUD для всех сущностей
- Расширенные RLS политики
- Дашборды и отчеты
- API документация
- Тесты

📋 **Планируется:**
- AI интеграции
- Workflow Engine
- Биллинг автоматизация
- Внешние интеграции (CRM, телефония)
- Mobile приложение
