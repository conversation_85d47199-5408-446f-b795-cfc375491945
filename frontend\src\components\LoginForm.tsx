import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { supabase } from '@/lib/supabase'
import { LogIn, Eye, EyeOff, AlertCircle } from 'lucide-react'

interface LoginFormData {
  email: string
  password: string
}

interface LoginFormProps {
  onSuccess: (user: any) => void
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>()

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      // ДЕМО РЕЖИМ - проверяем демо логины
      const demoUsers = {
        '<EMAIL>': {
          password: 'admin123',
          user: {
            id: 'demo-admin-id',
            email: '<EMAIL>',
            role: 'provider',
            tenant_id: 'demo-tenant-id',
            first_name: 'Demo',
            last_name: 'Admin',
            tenant: {
              tenant_id: 'demo-tenant-id',
              name: 'Demo Company',
              status: 'active'
            }
          }
        },
        '<EMAIL>': {
          password: 'reseller123',
          user: {
            id: 'demo-reseller-id',
            email: '<EMAIL>',
            role: 'reseller',
            tenant_id: 'demo-tenant-id',
            first_name: 'Demo',
            last_name: 'Reseller',
            tenant: {
              tenant_id: 'demo-tenant-id',
              name: 'Demo Reseller Company',
              status: 'active'
            }
          }
        },
        '<EMAIL>': {
          password: 'support123',
          user: {
            id: 'demo-support-id',
            email: '<EMAIL>',
            role: 'support',
            tenant_id: 'demo-tenant-id',
            first_name: 'Demo',
            last_name: 'Support',
            tenant: {
              tenant_id: 'demo-tenant-id',
              name: 'Demo Company',
              status: 'active'
            }
          }
        }
      }

      const demoUser = demoUsers[data.email as keyof typeof demoUsers]

      if (!demoUser || demoUser.password !== data.password) {
        throw new Error('Неверный email или пароль')
      }

      // Симулируем задержку
      await new Promise(resolve => setTimeout(resolve, 1000))

      onSuccess(demoUser.user)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
            <LogIn className="h-6 w-6 text-primary-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Вход в SPaaS Platform
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Введите ваши учетные данные для доступа к платформе
          </p>

          {/* Демо логины */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Демо аккаунты:</h3>
            <div className="space-y-1 text-xs text-blue-700">
              <div><strong>Provider:</strong> <EMAIL> / admin123</div>
              <div><strong>Reseller:</strong> <EMAIL> / reseller123</div>
              <div><strong>Support:</strong> <EMAIL> / support123</div>
            </div>
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Ошибка входа
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    {error}
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email адрес
              </label>
              <input
                {...register('email', {
                  required: 'Email обязателен',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Неверный формат email'
                  }
                })}
                type="email"
                autoComplete="email"
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="Введите email"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Пароль
              </label>
              <div className="mt-1 relative">
                <input
                  {...register('password', {
                    required: 'Пароль обязателен',
                    minLength: {
                      value: 6,
                      message: 'Пароль должен содержать минимум 6 символов'
                    }
                  })}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="Введите пароль"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                'Войти'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
