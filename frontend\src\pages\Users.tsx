import React, { useEffect, useState } from 'react'
import { Plus, Search, Filter, User, UserCheck, UserX, Shield, Edit, Trash2, Mail, Calendar } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate, getRoleDisplayName, getInitials } from '@/lib/utils'
import { demoUsers, demoStats } from '@/lib/demoData'

interface AppUser {
  user_id: string
  tenant_id: string
  email: string
  role: string
  first_name: string
  last_name: string
  is_active: boolean
  last_login_at: string | null
  created_at: string
  tenants?: {
    name: string
    status: string
  }
}

export const Users: React.FC = () => {
  const { user } = useAuthStore()
  const [users, setUsers] = useState<AppUser[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchUsers()
  }, [user])

  const fetchUsers = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setUsers(demoUsers as any)
      } else {
        // Здесь будет реальный запрос к Supabase
        setUsers([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredUsers = users.filter(appUser => {
    const matchesSearch = 
      appUser.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appUser.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appUser.last_name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = roleFilter === 'all' || appUser.role === roleFilter
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && appUser.is_active) ||
      (statusFilter === 'inactive' && !appUser.is_active)

    return matchesSearch && matchesRole && matchesStatus
  })

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'provider':
        return <Shield className="h-4 w-4 text-purple-600" />
      case 'reseller':
        return <User className="h-4 w-4 text-blue-600" />
      case 'admin':
        return <UserCheck className="h-4 w-4 text-green-600" />
      case 'support':
        return <User className="h-4 w-4 text-yellow-600" />
      default:
        return <User className="h-4 w-4 text-gray-400" />
    }
  }

  const getRoleColor = (role: string) => {
    const colors: Record<string, string> = {
      'provider': 'bg-purple-100 text-purple-800',
      'reseller': 'bg-blue-100 text-blue-800',
      'admin': 'bg-green-100 text-green-800',
      'support': 'bg-yellow-100 text-yellow-800',
      'staff': 'bg-gray-100 text-gray-800',
      'client': 'bg-indigo-100 text-indigo-800'
    }
    return colors[role] || 'bg-gray-100 text-gray-800'
  }

  const handleToggleStatus = (userId: string) => {
    setUsers(prev => prev.map(u => 
      u.user_id === userId
        ? { ...u, is_active: !u.is_active }
        : u
    ))
  }

  const handleDeleteUser = (userId: string) => {
    if (confirm('Вы уверены, что хотите удалить этого пользователя?')) {
      setUsers(prev => prev.filter(u => u.user_id !== userId))
    }
  }

  const canManageUser = (targetUser: AppUser) => {
    if (!user) return false
    
    // Provider может управлять всеми
    if (user.role === 'provider') return true
    
    // Admin может управлять support, staff, client
    if (user.role === 'admin') {
      return ['support', 'staff', 'client'].includes(targetUser.role)
    }
    
    // Остальные не могут управлять пользователями
    return false
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Пользователи</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление пользователями системы
          </p>
        </div>
        {(user?.role === 'provider' || user?.role === 'admin') && (
          <button className="btn btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Добавить пользователя
          </button>
        )}
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon blue">
            <User className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего пользователей</h3>
            <p>{demoStats.totalUsers}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <UserCheck className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Активные</h3>
            <p>{demoStats.activeUsers}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon red">
            <UserX className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Неактивные</h3>
            <p>{demoStats.inactiveUsers}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <Shield className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Администраторы</h3>
            <p>{users.filter(u => ['provider', 'admin'].includes(u.role)).length}</p>
          </div>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="card">
        <div className="card-header">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Поиск пользователей..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            {/* Role Filter */}
            <div className="sm:w-48">
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все роли</option>
                <option value="provider">Провайдер</option>
                <option value="reseller">Реселлер</option>
                <option value="admin">Администратор</option>
                <option value="support">Поддержка</option>
                <option value="staff">Сотрудник</option>
                <option value="client">Клиент</option>
              </select>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все статусы</option>
                <option value="active">Активные</option>
                <option value="inactive">Неактивные</option>
              </select>
            </div>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>Пользователь</th>
                <th>Email</th>
                <th>Роль</th>
                <th>Статус</th>
                <th>Последний вход</th>
                <th>Создан</th>
                <th>Действия</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((appUser) => (
                <tr key={appUser.user_id}>
                  <td>
                    <div className="flex items-center">
                      <div className="avatar mr-3">
                        {getInitials(appUser.first_name, appUser.last_name)}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {appUser.first_name} {appUser.last_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {appUser.user_id.slice(0, 8)}...
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">{appUser.email}</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getRoleIcon(appUser.role)}
                      <span className={`ml-2 badge ${getRoleColor(appUser.role)}`}>
                        {getRoleDisplayName(appUser.role)}
                      </span>
                    </div>
                  </td>
                  <td>
                    <span className={`badge ${appUser.is_active ? 'active' : 'inactive'}`}>
                      {appUser.is_active ? 'Активен' : 'Неактивен'}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-1" />
                      {appUser.last_login_at ? formatDate(appUser.last_login_at) : 'Никогда'}
                    </div>
                  </td>
                  <td className="text-sm text-gray-500">
                    {formatDate(appUser.created_at)}
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      {canManageUser(appUser) && (
                        <>
                          <button
                            onClick={() => handleToggleStatus(appUser.user_id)}
                            className={`p-1 text-gray-400 hover:${appUser.is_active ? 'text-red-600' : 'text-green-600'}`}
                            title={appUser.is_active ? 'Деактивировать' : 'Активировать'}
                          >
                            {appUser.is_active ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
                          </button>
                          
                          <button
                            className="p-1 text-gray-400 hover:text-blue-600"
                            title="Редактировать"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          
                          {appUser.user_id !== user?.id && (
                            <button
                              onClick={() => handleDeleteUser(appUser.user_id)}
                              className="p-1 text-gray-400 hover:text-red-600"
                              title="Удалить"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredUsers.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm || roleFilter !== 'all' || statusFilter !== 'all'
                  ? 'Пользователи не найдены по заданным критериям'
                  : 'Пока нет пользователей'
                }
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
