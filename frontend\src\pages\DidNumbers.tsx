import React, { useEffect, useState } from 'react'
import { Plus, Search, Filter, Phone, PhoneCall, PhoneOff, Globe, MapPin, Edit, Trash2, Link, Unlink, ShoppingCart, History, Eye } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { demoDidNumbers, demoStats } from '@/lib/demoData'
import { NumberPurchaseModal } from '@/components/NumberPurchaseModal'
import { PurchaseHistoryModal } from '@/components/PurchaseHistoryModal'

interface DidNumber {
  did_number_id: string
  tenant_id: string
  number: string
  country_code: string
  city: string
  region?: string
  number_type: string // 'local', 'toll_free', 'mobile', 'shortcode'
  capabilities: string[] // ['voice', 'sms', 'mms', 'fax']
  status: string
  assigned_to: string | null
  monthly_cost: number
  setup_cost: number
  provider: string
  // Информация о покупке
  purchased_by?: string
  purchased_at?: string
  purchase_price?: number
  purchase_notes?: string
  // Назначение
  created_at: string
  sip_accounts?: {
    username: string
    display_name: string
  } | null
  clients?: {
    company_name: string
    contact_person: string
  } | null
  // Информация о покупателе
  purchased_by_user?: {
    first_name: string
    last_name: string
    email: string
  } | null
}

export const DidNumbers: React.FC = () => {
  const { user } = useAuthStore()
  const [didNumbers, setDidNumbers] = useState<DidNumber[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [countryFilter, setCountryFilter] = useState<string>('all')

  // Модальные окна
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [showHistoryModal, setShowHistoryModal] = useState(false)

  useEffect(() => {
    fetchDidNumbers()
  }, [user])

  const fetchDidNumbers = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // В демо режиме используем локальные данные
      if (user.email.includes('@demo.com')) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setDidNumbers(demoDidNumbers as any)
      } else {
        // Здесь будет реальный запрос к Supabase
        setDidNumbers([])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredDidNumbers = didNumbers.filter(didNumber => {
    const matchesSearch = 
      didNumber.number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      didNumber.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      didNumber.provider?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || didNumber.status === statusFilter
    const matchesCountry = countryFilter === 'all' || didNumber.country_code === countryFilter

    return matchesSearch && matchesStatus && matchesCountry
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Link className="h-4 w-4 text-green-600" />
      case 'available':
        return <PhoneCall className="h-4 w-4 text-blue-600" />
      case 'reserved':
        return <Phone className="h-4 w-4 text-yellow-600" />
      case 'suspended':
        return <PhoneOff className="h-4 w-4 text-red-600" />
      default:
        return <Phone className="h-4 w-4 text-gray-400" />
    }
  }

  const getCountryFlag = (countryCode: string) => {
    const flags: Record<string, string> = {
      'RU': '🇷🇺',
      'US': '🇺🇸',
      'GB': '🇬🇧',
      'DE': '🇩🇪',
      'FR': '🇫🇷'
    }
    return flags[countryCode] || '🌍'
  }

  const handleAssignNumber = (didNumberId: string) => {
    // В демо режиме просто обновляем локальный массив
    setDidNumbers(prev => prev.map(did => 
      did.did_number_id === didNumberId
        ? { ...did, status: 'assigned', assigned_to: 'sip-demo' }
        : did
    ))
  }

  const handleUnassignNumber = (didNumberId: string) => {
    if (confirm('Вы уверены, что хотите отвязать этот номер?')) {
      setDidNumbers(prev => prev.map(did => 
        did.did_number_id === didNumberId
          ? { ...did, status: 'available', assigned_to: null, sip_accounts: null, clients: null }
          : did
      ))
    }
  }

  const handleDeleteNumber = (didNumberId: string) => {
    if (confirm('Вы уверены, что хотите удалить этот DID номер?')) {
      setDidNumbers(prev => prev.filter(did => did.did_number_id !== didNumberId))
    }
  }

  const handlePurchaseNumbers = async (numbers: any[], notes: string) => {
    try {
      // В демо режиме просто добавляем номера в список
      const newDidNumbers = numbers.map((number, index) => ({
        did_number_id: `did-${Date.now()}-${index}`,
        tenant_id: user?.tenant_id || '',
        number: number.number,
        country_code: number.country_code,
        city: number.city,
        region: number.region,
        number_type: number.number_type,
        capabilities: number.capabilities,
        status: 'available',
        assigned_to: null,
        monthly_cost: number.monthly_cost,
        setup_cost: number.setup_cost,
        provider: number.provider,
        purchased_by: user?.id || '',
        purchased_at: new Date().toISOString(),
        purchase_price: number.setup_cost + number.monthly_cost,
        purchase_notes: notes,
        created_at: new Date().toISOString(),
        purchased_by_user: {
          first_name: user?.first_name || '',
          last_name: user?.last_name || '',
          email: user?.email || ''
        }
      }))

      setDidNumbers(prev => [...prev, ...newDidNumbers])

      // Показываем уведомление об успешной покупке
      alert(`Успешно приобретено ${numbers.length} номеров!`)
    } catch (error) {
      console.error('Error purchasing numbers:', error)
      alert('Ошибка при покупке номеров')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">DID Номера</h1>
          <p className="mt-1 text-sm text-gray-500">
            Управление входящими номерами
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowHistoryModal(true)}
            className="btn btn-secondary"
          >
            <History className="h-4 w-4 mr-2" />
            История покупок
          </button>
          <button
            onClick={() => setShowPurchaseModal(true)}
            className="btn btn-primary"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Купить номера
          </button>
          <button className="btn btn-secondary">
            <Plus className="h-4 w-4 mr-2" />
            Добавить номер
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon green">
            <Link className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Назначенные</h3>
            <p>{demoStats.assignedDidNumbers}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon blue">
            <PhoneCall className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Доступные</h3>
            <p>{demoStats.availableDidNumbers}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon yellow">
            <Phone className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Зарезервированные</h3>
            <p>{didNumbers.filter(d => d.status === 'reserved').length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon purple">
            <Globe className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Всего номеров</h3>
            <p>{didNumbers.length}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon green">
            <MapPin className="h-6 w-6" />
          </div>
          <div className="stat-content">
            <h3>Месячная стоимость</h3>
            <p>{formatCurrency(didNumbers.reduce((sum, did) => sum + did.monthly_cost, 0))}</p>
          </div>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="card">
        <div className="card-header">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="search-box">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Поиск DID номеров..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все статусы</option>
                <option value="available">Доступные</option>
                <option value="assigned">Назначенные</option>
                <option value="reserved">Зарезервированные</option>
                <option value="suspended">Приостановленные</option>
              </select>
            </div>

            {/* Country Filter */}
            <div className="sm:w-48">
              <select
                value={countryFilter}
                onChange={(e) => setCountryFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">Все страны</option>
                <option value="RU">🇷🇺 Россия</option>
                <option value="US">🇺🇸 США</option>
                <option value="GB">🇬🇧 Великобритания</option>
                <option value="DE">🇩🇪 Германия</option>
              </select>
            </div>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>Номер</th>
                <th>Местоположение</th>
                <th>Статус</th>
                <th>Назначен</th>
                <th>Стоимость</th>
                <th>Провайдер</th>
                <th>Покупка</th>
                <th>Действия</th>
              </tr>
            </thead>
            <tbody>
              {filteredDidNumbers.map((didNumber) => (
                <tr key={didNumber.did_number_id}>
                  <td>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="font-medium text-gray-900">
                        {didNumber.number}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <span className="mr-2">{getCountryFlag(didNumber.country_code)}</span>
                      <div>
                        <div className="text-sm text-gray-900">{didNumber.city}</div>
                        <div className="text-xs text-gray-500">{didNumber.country_code}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getStatusIcon(didNumber.status)}
                      <span className={`ml-2 badge ${didNumber.status}`}>
                        {didNumber.status}
                      </span>
                    </div>
                  </td>
                  <td>
                    {didNumber.status === 'assigned' && didNumber.sip_accounts ? (
                      <div>
                        <div className="text-sm text-gray-900">
                          {didNumber.sip_accounts.username} - {didNumber.sip_accounts.display_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {didNumber.clients?.company_name}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Не назначен</span>
                    )}
                  </td>
                  <td>
                    <div>
                      <div className="text-sm text-gray-900">
                        {formatCurrency(didNumber.monthly_cost)}/мес
                      </div>
                      {didNumber.setup_cost > 0 && (
                        <div className="text-xs text-gray-500">
                          Подключение: {formatCurrency(didNumber.setup_cost)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="text-sm text-gray-500">
                    {didNumber.provider}
                  </td>
                  <td>
                    {didNumber.purchased_by_user ? (
                      <div>
                        <div className="text-sm text-gray-900">
                          {didNumber.purchased_by_user.first_name} {didNumber.purchased_by_user.last_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {didNumber.purchased_at ? formatDate(didNumber.purchased_at) : ''}
                        </div>
                        {didNumber.purchase_notes && (
                          <div className="text-xs text-blue-600 truncate max-w-32" title={didNumber.purchase_notes}>
                            {didNumber.purchase_notes}
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400">Нет данных</span>
                    )}
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      {didNumber.status === 'available' ? (
                        <button
                          onClick={() => handleAssignNumber(didNumber.did_number_id)}
                          className="p-1 text-gray-400 hover:text-green-600"
                          title="Назначить"
                        >
                          <Link className="h-4 w-4" />
                        </button>
                      ) : didNumber.status === 'assigned' ? (
                        <button
                          onClick={() => handleUnassignNumber(didNumber.did_number_id)}
                          className="p-1 text-gray-400 hover:text-yellow-600"
                          title="Отвязать"
                        >
                          <Unlink className="h-4 w-4" />
                        </button>
                      ) : null}
                      
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Редактировать"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => handleDeleteNumber(didNumber.did_number_id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Удалить"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredDidNumbers.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm || statusFilter !== 'all' || countryFilter !== 'all'
                  ? 'DID номера не найдены по заданным критериям'
                  : 'Пока нет DID номеров'
                }
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Модальные окна */}
      <NumberPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onPurchase={handlePurchaseNumbers}
      />

      <PurchaseHistoryModal
        isOpen={showHistoryModal}
        onClose={() => setShowHistoryModal(false)}
      />
    </div>
  )
}
