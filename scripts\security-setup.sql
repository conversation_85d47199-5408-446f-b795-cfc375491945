-- Скрипт настройки безопасности для продакшн
-- Выполнить в Supabase SQL Editor с правами service_role

\echo 'Настройка безопасности SPaaS Platform...';

-- 1. Настройки аутентификации
\echo 'Настройка аутентификации...';

-- Отключить публичную регистрацию (выполнить в Dashboard)
-- UPDATE auth.config SET enable_signup = false;

-- 2. Функция для мониторинга безопасности
\echo 'Создание функций мониторинга...';

CREATE OR REPLACE FUNCTION monitor_suspicious_activity()
RETURNS TRIGGER AS $$
DECLARE
    failed_attempts INTEGER;
    alert_data JSONB;
BEGIN
    -- Мониторинг множественных неудачных попыток входа
    IF NEW.action = 'LOGIN_FAILED' THEN
        SELECT COUNT(*) INTO failed_attempts
        FROM activity_logs 
        WHERE action = 'LOGIN_FAILED' 
        AND (NEW.ip_address IS NOT NULL AND ip_address = NEW.ip_address)
        AND created_at > NOW() - INTERVAL '15 minutes';
        
        IF failed_attempts >= 5 THEN
            alert_data := jsonb_build_object(
                'type', 'multiple_failed_logins',
                'ip_address', NEW.ip_address,
                'attempts', failed_attempts,
                'time_window', '15 minutes',
                'severity', 'high'
            );
            
            PERFORM pg_notify('security_alert', alert_data::text);
            
            -- Логируем алерт
            INSERT INTO activity_logs (
                tenant_id, user_id, action, table_name, 
                record_id, changes, ip_address
            ) VALUES (
                NEW.tenant_id, NULL, 'SECURITY_ALERT', 'auth',
                NULL, alert_data, NEW.ip_address
            );
        END IF;
    END IF;
    
    -- Мониторинг подозрительных изменений критических данных
    IF NEW.action IN ('DELETE', 'UPDATE') AND NEW.table_name IN ('users', 'tenants', 'invoices') THEN
        alert_data := jsonb_build_object(
            'type', 'sensitive_data_change',
            'user_id', NEW.user_id,
            'table_name', NEW.table_name,
            'record_id', NEW.record_id,
            'action', NEW.action,
            'severity', 'medium'
        );
        
        PERFORM pg_notify('security_alert', alert_data::text);
    END IF;
    
    -- Мониторинг массовых операций
    IF NEW.action = 'DELETE' THEN
        DECLARE
            recent_deletes INTEGER;
        BEGIN
            SELECT COUNT(*) INTO recent_deletes
            FROM activity_logs 
            WHERE action = 'DELETE' 
            AND user_id = NEW.user_id
            AND created_at > NOW() - INTERVAL '5 minutes';
            
            IF recent_deletes >= 10 THEN
                alert_data := jsonb_build_object(
                    'type', 'mass_deletion',
                    'user_id', NEW.user_id,
                    'deletions', recent_deletes,
                    'time_window', '5 minutes',
                    'severity', 'critical'
                );
                
                PERFORM pg_notify('security_alert', alert_data::text);
            END IF;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Создать триггер мониторинга
DROP TRIGGER IF EXISTS monitor_suspicious_activity_trigger ON activity_logs;
CREATE TRIGGER monitor_suspicious_activity_trigger
    AFTER INSERT ON activity_logs
    FOR EACH ROW
    EXECUTE FUNCTION monitor_suspicious_activity();

-- 3. Функция для очистки старых логов
\echo 'Создание функции очистки логов...';

CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Удаляем логи старше 90 дней
    DELETE FROM activity_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Логируем операцию очистки
    INSERT INTO activity_logs (
        tenant_id, user_id, action, table_name, 
        record_id, changes
    ) VALUES (
        NULL, NULL, 'CLEANUP', 'activity_logs',
        NULL, jsonb_build_object('deleted_records', deleted_count)
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Функция для проверки целостности данных
\echo 'Создание функции проверки целостности...';

CREATE OR REPLACE FUNCTION check_data_integrity()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    details JSONB
) AS $$
BEGIN
    -- Проверка орфанных записей в users
    RETURN QUERY
    SELECT 
        'orphaned_users'::TEXT,
        CASE WHEN COUNT(*) = 0 THEN 'OK' ELSE 'FAIL' END::TEXT,
        jsonb_build_object('count', COUNT(*))
    FROM users u
    LEFT JOIN tenants t ON u.tenant_id = t.tenant_id
    WHERE t.tenant_id IS NULL;
    
    -- Проверка орфанных записей в clients
    RETURN QUERY
    SELECT 
        'orphaned_clients'::TEXT,
        CASE WHEN COUNT(*) = 0 THEN 'OK' ELSE 'FAIL' END::TEXT,
        jsonb_build_object('count', COUNT(*))
    FROM clients c
    LEFT JOIN tenants t ON c.tenant_id = t.tenant_id
    WHERE t.tenant_id IS NULL;
    
    -- Проверка SIP аккаунтов без клиентов
    RETURN QUERY
    SELECT 
        'sip_accounts_without_clients'::TEXT,
        CASE WHEN COUNT(*) = 0 THEN 'OK' ELSE 'FAIL' END::TEXT,
        jsonb_build_object('count', COUNT(*))
    FROM sip_accounts s
    LEFT JOIN clients c ON s.client_id = c.client_id
    WHERE c.client_id IS NULL;
    
    -- Проверка инвойсов без клиентов
    RETURN QUERY
    SELECT 
        'invoices_without_clients'::TEXT,
        CASE WHEN COUNT(*) = 0 THEN 'OK' ELSE 'FAIL' END::TEXT,
        jsonb_build_object('count', COUNT(*))
    FROM invoices i
    LEFT JOIN clients c ON i.client_id = c.client_id
    WHERE c.client_id IS NULL;
    
    -- Проверка балансов клиентов
    RETURN QUERY
    SELECT 
        'negative_balances'::TEXT,
        CASE WHEN COUNT(*) = 0 THEN 'OK' ELSE 'WARNING' END::TEXT,
        jsonb_build_object('count', COUNT(*), 'total_debt', SUM(ABS(current_balance)))
    FROM clients
    WHERE current_balance < -credit_limit;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Функция для генерации отчета безопасности
\echo 'Создание функции отчета безопасности...';

CREATE OR REPLACE FUNCTION security_report(days_back INTEGER DEFAULT 7)
RETURNS TABLE(
    metric TEXT,
    value BIGINT,
    details JSONB
) AS $$
BEGIN
    -- Количество попыток входа
    RETURN QUERY
    SELECT 
        'login_attempts'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_build_object(
            'successful', COUNT(*) FILTER (WHERE action = 'LOGIN'),
            'failed', COUNT(*) FILTER (WHERE action = 'LOGIN_FAILED')
        )
    FROM activity_logs
    WHERE action IN ('LOGIN', 'LOGIN_FAILED')
    AND created_at > NOW() - (days_back || ' days')::INTERVAL;
    
    -- Уникальные IP адреса
    RETURN QUERY
    SELECT 
        'unique_ips'::TEXT,
        COUNT(DISTINCT ip_address)::BIGINT,
        jsonb_build_object('period_days', days_back)
    FROM activity_logs
    WHERE ip_address IS NOT NULL
    AND created_at > NOW() - (days_back || ' days')::INTERVAL;
    
    -- Подозрительная активность
    RETURN QUERY
    SELECT 
        'security_alerts'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_build_object(
            'types', jsonb_agg(DISTINCT (changes->>'type'))
        )
    FROM activity_logs
    WHERE action = 'SECURITY_ALERT'
    AND created_at > NOW() - (days_back || ' days')::INTERVAL;
    
    -- Активные пользователи
    RETURN QUERY
    SELECT 
        'active_users'::TEXT,
        COUNT(DISTINCT user_id)::BIGINT,
        jsonb_build_object('period_days', days_back)
    FROM activity_logs
    WHERE user_id IS NOT NULL
    AND created_at > NOW() - (days_back || ' days')::INTERVAL;
    
    -- Операции с данными
    RETURN QUERY
    SELECT 
        'data_operations'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_build_object(
            'creates', COUNT(*) FILTER (WHERE action = 'CREATE'),
            'updates', COUNT(*) FILTER (WHERE action = 'UPDATE'),
            'deletes', COUNT(*) FILTER (WHERE action = 'DELETE')
        )
    FROM activity_logs
    WHERE action IN ('CREATE', 'UPDATE', 'DELETE')
    AND created_at > NOW() - (days_back || ' days')::INTERVAL;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Создание индексов для производительности
\echo 'Создание индексов безопасности...';

-- Индексы для быстрого поиска в логах
CREATE INDEX IF NOT EXISTS idx_activity_logs_security 
ON activity_logs(action, ip_address, created_at) 
WHERE action IN ('LOGIN', 'LOGIN_FAILED', 'SECURITY_ALERT');

CREATE INDEX IF NOT EXISTS idx_activity_logs_user_time 
ON activity_logs(user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_activity_logs_table_action 
ON activity_logs(table_name, action, created_at);

-- 7. Создание представлений для мониторинга
\echo 'Создание представлений мониторинга...';

CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    'login_attempts_today' as metric,
    COUNT(*) as value,
    'Попытки входа за сегодня' as description
FROM activity_logs 
WHERE action IN ('LOGIN', 'LOGIN_FAILED') 
AND created_at >= CURRENT_DATE

UNION ALL

SELECT 
    'failed_logins_today',
    COUNT(*),
    'Неудачные попытки входа за сегодня'
FROM activity_logs 
WHERE action = 'LOGIN_FAILED' 
AND created_at >= CURRENT_DATE

UNION ALL

SELECT 
    'active_users_today',
    COUNT(DISTINCT user_id),
    'Активные пользователи за сегодня'
FROM activity_logs 
WHERE user_id IS NOT NULL 
AND created_at >= CURRENT_DATE

UNION ALL

SELECT 
    'security_alerts_week',
    COUNT(*),
    'Алерты безопасности за неделю'
FROM activity_logs 
WHERE action = 'SECURITY_ALERT' 
AND created_at >= CURRENT_DATE - INTERVAL '7 days';

-- Предоставить доступ к представлению
GRANT SELECT ON security_dashboard TO authenticated;

-- 8. Настройка уведомлений (PostgreSQL LISTEN/NOTIFY)
\echo 'Настройка системы уведомлений...';

-- Функция для обработки алертов безопасности
CREATE OR REPLACE FUNCTION handle_security_alert()
RETURNS TRIGGER AS $$
BEGIN
    -- Отправляем уведомление через NOTIFY
    PERFORM pg_notify('security_channel', NEW.changes::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггер для алертов
DROP TRIGGER IF EXISTS security_alert_trigger ON activity_logs;
CREATE TRIGGER security_alert_trigger
    AFTER INSERT ON activity_logs
    FOR EACH ROW
    WHEN (NEW.action = 'SECURITY_ALERT')
    EXECUTE FUNCTION handle_security_alert();

\echo 'Настройка безопасности завершена!';
\echo '';
\echo 'Следующие шаги:';
\echo '1. Настройте CORS в Supabase Dashboard';
\echo '2. Настройте Rate Limiting в Supabase Dashboard';
\echo '3. Отключите публичную регистрацию в Auth Settings';
\echo '4. Настройте мониторинг алертов в вашем приложении';
\echo '5. Запланируйте регулярную очистку логов: SELECT cleanup_old_logs();';
\echo '6. Настройте регулярные проверки: SELECT * FROM check_data_integrity();';
\echo '7. Просматривайте отчеты: SELECT * FROM security_report(7);';
\echo '';
\echo 'Для мониторинга используйте: SELECT * FROM security_dashboard;';
